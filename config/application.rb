require 'active_support/hash_with_indifferent_access'

require File.expand_path('../boot', __FILE__)
require 'yaml'
APP_CONFIG = YAML.load_file(File.expand_path('../app_config.yml', __FILE__))
NETWORKS_CONFIG = YAML.load_file(File.expand_path('../networks.yml', __FILE__))
ANALYTICS_SETUP  = YAML.load_file(File.expand_path('../analytics.yml', __FILE__) )

require 'rails/all'

#support for locale fallbacks
require "i18n/backend/fallbacks"
I18n::Backend::Simple.send(:include, I18n::Backend::Fallbacks)

require 'will_paginate/array'

if defined?(Bundler)
  # If you precompile assets before deploying to production, use this line
  #Bundler.require(*Rails.groups(:assets => %w(development test)))
  # If you want your assets lazily compiled in production, use this line
  Bundler.require(:default, :assets, Rails.env)
end

module Gp
  class Application < Rails::Application

    #config.middleware.insert_after ActionDispatch::Static, Rack::Deflater
    # config.threadsafe!
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.
    config.paperclip_defaults = {
        :validate_media_type => false
    }
    config.eager_load_paths += Dir[ Rails.root.join('app', 'services', 'mkp', 'shipping', 'rates', '**/') ]
    config.eager_load_paths += Dir[ Rails.root.join('app', 'services', 'mkp', 'shipping', 'rates.rb') ]
    config.eager_load_paths += Dir[ Rails.root.join('app', 'services', 'mkp', 'shipping', '*') ]
    config.eager_load_paths += Dir[ Rails.root.join('app', 'services', '**/*') ]
    config.eager_load_paths += Dir[ Rails.root.join('app', 'controllers', "concerns", '**/') ]
    config.eager_load_paths += Dir["#{config.root}/lib", "#{config.root}/lib/**/"]
    # Custom directories with classes and modules you want to be autoloadable.
    config.autoload_paths += Dir[ Rails.root.join('app', 'controllers', "concerns", '**/') ]
    config.autoload_paths += Dir["#{config.root}/lib", "#{config.root}/lib/**/"]

    # Only load the plugins named here, in the order given (default is alphabetical).
    # :all can be used as a placeholder for all plugins not explicitly named.
    # config.plugins = [ :exception_notification, :ssl_requirement, :all ]

    # CAVI: Para debug del cache localmente
    # config.cache_store = :file_store, File.join(File.dirname(__FILE__), '../', 'tmp', 'cache') #

    config.cache_store = :redis_store, { host: APP_CONFIG[Rails.env]['redis']['host'],
                                         port: APP_CONFIG[Rails.env]['redis']['port'],
                                         db: APP_CONFIG[Rails.env]['redis']['db'],
                                         namespace: 'cache',
                                         expires_in: 7.days }

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.

    config.time_zone = 'Buenos Aires'

    config.action_mailer.default_url_options = { host: APP_CONFIG[Rails.env]['hostname'] }

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('config', 'locales', '**/*.{rb,yml}').to_s]

    # Rails will fallback to config.i18n.default_locale translation
    # TODO: This shouldn't be here in the first place.
    localized_networks = NETWORKS_CONFIG.select do |_, config|
      config['locale'].present?
    end

    locale_configs = localized_networks.each_with_object({}) do |(_, config), result|
      if config['fallback_locale'].present?
        locale = config['locale']
        result[locale] = config['fallback_locale']
      end
    end

    config.i18n.fallbacks = locale_configs

    # Configure the default encoding used in templates for Ruby 1.9.
    config.encoding = "utf-8"

    # Configure sensitive parameters which will be filtered from the log file.
    config.filter_parameters += [:password,
                                 :cvv,
                                 :cardholder_name,
                                 :card_holder_name,
                                 :card_expiration_month,
                                 :card_brand,
                                 :card_expiration_year,
                                 :number,
                                 :card_number,
                                 :security_code,
                                 :card_holder_identification]

    # Enforce whitelist mode for mass assignment.
    # This will create an empty whitelist of attributes available for mass-assignment for all models
    # in your app. As such, your models will need to explicitly whitelist or blacklist accessible
    # parameters by using an attr_accessible or attr_protected declaration.
    # config.active_record.whitelist_attributes = true

    config.assets.paths << Rails.root.join('app', 'assets', 'fonts')
    config.assets.precompile += %w(.svg .eot .woff .ttf)

    config.assets.precompile += %w[mkp/application.css
                                   social/application.css
                                   distribution/distribution.css
                                   widgets/shop_products.css
                                   v5/application.css
                                   v5/mkp/checkout.css
                                   v5/mobile/application.css
                                   v5/mobile/mkp/checkout.css
                                   v5/mailer/general.css
                                   fonts/Lato.css]

    config.assets.precompile += %w[mkp/application.js
                                   mkp/polyfills.js
                                   social/application.js
                                   distribution/distribution.js
                                   modernizr.js
                                   v5/application.js
                                   v5/mkp/checkout.js
                                   v5/mobile/application.js
                                   v5/mobile/mkp/checkout.js]

    # Enable the asset pipeline
    config.assets.enabled = true

    # don't generate RSpec tests for views and helpers
    config.generators do |g|
      g.template_engine :slim
      g.test_framework :rspec, fixture: true
      g.fixture_replacement :factory_girl, dir: 'spec/factories'
      g.assets = false
      g.helper = false
      g.view_specs false
      g.helper_specs false
    end

    # To avoid the IpSpoofAttackError we disabled this because
    # we have RackAttack set it up, but we should check if we are preventing
    # this kind of situation with it though.
    config.action_dispatch.ip_spoofing_check = false

    #config.middleware.use Rack::Attack if Rails.env.production?

    config.middleware.insert_before 0, "Rack::Cors" do #, :debug => true, :logger => (-> { Rails.logger }) do
    allow do
      origins '*'
      resource '*',
               :headers => :any,
               :methods => [:get, :post, :delete, :put, :patch, :options, :head]
    end
    end

    config.i18n.enforce_available_locales = false
    config.i18n.default_locale = :es

    config.assets.image_optim = false
    config.assets.compress = false
    config.assets.digest = true

    # route errors pages
    config.exceptions_app = self.routes
  end
end
