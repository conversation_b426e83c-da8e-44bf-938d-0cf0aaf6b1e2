namespace :gp do
  namespace :shipments do
    desc 'Update krabpack labels status'
    task refresh: :environment do
      Rails.logger.info("------------ STARTED UPDATE KRABPACK LABELS STATUS. #{Time.zone.now}")

      labels = Mkp::ShipmentLabel.joins(:shipment).where('mkp_shipment_labels.gateway = ? AND mkp_shipments.status not in (?)', 'Krabpack', %w[delivered cancelled not_delivered])
      Rails.logger.info("--- #{labels.count} labels for update")

      labels.reject { |label| label.shipment.order.blank? }.each do |label|
        service = Gateways::Shipments::Krabpack::GetShipmentService
                  .new(label: label)
        is_valid = service.perform
        if is_valid
          if label.shipment.not_delivered? && label.shipment.order.store.name == 'bancomacro' && label.shipment.shipment_kind_label == 'Envio'
            Rails.logger.info('----- envío regular de macro no entregado, cancelando pagos en puntos')
            label.shipment.suborders.first.payments.where(status: 'collected', gateway: 'VisaPuntos').each do |payment|
              Rails.logger.info("----- cancelando pago ##{payment.id}")
              payment.cancel!(label.shipment.order.store)
              Rails.logger.info("----- pago ##{payment.id} cancelado!")
            end
          end
        else
          Rails.logger.info("----- #{service.error}")
        end
      end
    end

    task fulfil_pending_shipments: :environment do
      Rails.logger.info('------------ Fulfil Pending KRABPACK and COATI shipments -------------')
      Gateways::Shipments::Fulfiller.process_all_unfulfilled
    end
  end

  Rake::Task['gp:shipments:fulfil_pending_shipments'].enhance do
    Rake::Task['gp:shipments:refresh'].execute
  end
end
