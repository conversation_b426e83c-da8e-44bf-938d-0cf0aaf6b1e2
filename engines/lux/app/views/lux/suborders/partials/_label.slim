- label_settings = Couriers.get_settings_for(label.courier)
.label
  .info class="#{'with-icon' if label_settings.present? && label_settings.icon.present?}"
    - if label_settings.present?
      - if label_settings.icon.present?
        = image_tag label_settings.icon, size: '32x32', alt: label_settings.name
      h4 = label_settings.name
      .tracking
        - if label.ready?
          -if label.tracking_number.present?
            span.number= label.tracking_number
            span -
          - if label.gateway.downcase == "coati"
            span.url= link_to t('.tracking_url'), "#{COATI_TRACKING_URL}#{label.tracking_number}", target: '_blank'
          - elsif label.tracking_url.present?
            span.url= link_to t('.tracking_url'), label.tracking_url, target: '_blank'
        - else
          span.message= t('.incomplete_html')
    - else
      h4 = "#{label.courier.titleize} (#{label.shipment.shipment_kind_label})"
      .tracking
        span.number= label.tracking_number
  - if label.ready?
    .status
      span class="#{label.shipment.status}"= t(".status.#{label.shipment.status}")
    .general-actions
      .details
        = "#{t('.shipment-updated-at')}: #{label.shipment.updated_at.strftime('%d-%m-%Y %H:%M')}"
  - if can_print_label?(label) && (label_url = label.url).present?
    .downloads
      .message=t('.shipping_documents')
      = link_to t('.print_label'), label_url, target: '_blank'
      - extra_docs = label.extra_docs
      - if label.gateway == "Pickup"
        = link_to t('.print_lion_label'), "/manage/ar/shipments/#{suborder.shipment.id}.pdf", target: '_blank'
      - if extra_docs.present?
        - extra_docs.each do |doc|
          = link_to "#{doc[:form_type].titleize} (*)", doc[:form_url], target: '_blank'
  - unless label.shipment.status == 'delivered' || label.gateway == "Pickit"
    .actions
      - deliver_endpoint_path = ajax_labels_status_path(label, suborder_id: suborder.id, shipment_id: label.shipment.id, status: 'delivered')
      - cancel_endpoint_path = ajax_labels_status_path(label, suborder_id: suborder.id, shipment_id: label.shipment.id, status: 'cancel')
      - if suborder.shop.delivery_by_matrix || suborder.fulfilled_by_gp?
        = link_to t('.deliver'), '#delivering', "data-endpoint" => deliver_endpoint_path, class: 'deliver'
      - if label.cancellable?
        = link_to t('.cancel'), '#canceling', "data-endpoint" => cancel_endpoint_path, class: 'cancel'
    - if can_print_label?(label) && (label_url = label.url).present?
      .downloads
        - label.thermal_formats?
          .message=t('.other_label_formats')
          = link_to t('.zpl'), label.url(:zpl), target: '_blank'
          = link_to t('.epl2'), label.url(:epl2), target: '_blank'
        - if extra_docs.present?
          .message
            strong=t('.forms_comment')
            =t('.forms_commercial_invoice_explanation')
