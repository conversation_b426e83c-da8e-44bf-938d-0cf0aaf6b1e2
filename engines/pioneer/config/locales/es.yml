#Traducciones
es:
  pioneer:
    answers:
      title: Blacklist validación de identidad
      id: ID
      tienda: ID_Tienda
      customer_dni: CUIT/Documento
      search-by: Buscar por CUIT/Documento
      attempts: Intentos
      temporary_block: Bloqueo Temporal
      permanent_block: Bloqueo Permanente
      created_at: Fecha
      search: Buscar
      filter: Filtrar
      are_you_sure: ¿Esta seguro?
      actions: Acciones
      delete: Borrar DNI
      type: Servicio
    blacklist:
      title: Blacklist Primera Compra
      id: ID
      doc_type: Tipo de Documento
      doc_number: Nro de Documento
      search-by: Buscar por Documento
      search: Buscar
      filter: Filtrar
      are_you_sure: ¿Esta seguro?
      actions: Acciones
      delete: Borrar DNI
      store: Tienda
      type: Servicio
    bna:
      office:
        creation_succeeded: 'Sucursal creada con exito!'
        createtion_failed: 'Hubo un error creando la sucursal'
        create_office: 'Crear Sucursal'
        find_office: 'Buscar Office'
        base_name: 'Sucursales'
        update_succeeded: 'Datos actualizados con exito!'
        update_failed: 'Error al actualizar datos'
        specific: 'Sucursal %{token}'
    customers:
        title: Usuarios
        filter: Buscar
        filter-creation-date: Fecha de Creación
        filter-from: Desde
        filter-to: Has<PERSON>
        create: Crear Usuario
        export: Export.csv
    reservations:
      title: Reservas
      id: ID
      customer_dni: CUIT/Documento
      product: Producto
      store: Tienda
      maximum_lended_amount: Monto maximo del prestamo
      approved_lended_amount: Monto aprobado
      search-by: Buscar por CUIT/Documento
      search: Buscar
      filter: Filtrar
      are_you_sure: ¿Esta seguro?
      actions: Acciones
      delete: Borrar reserva
    menu:
      #Marketplace
      marketplace: Marketplace
      shop: Proveedores
      integrations: Integraciones
      stores: Tiendas
      categories: Categorías
      variant-costs: Costos
      products: Productos
      vouchers: Vouchers
      orders: Órdenes
      user-points: Puntos por usuario
      product-points: Puntos por productos
      abandoned-carts: Carros abandonados
      #Menu
      menu: Menú
      sort: Ordenar
      manage: Ajustar
      #Marketing
      marketing: Marketing
      coupons: Cupones
      cucardas: Cucardas
      promotions: Promociones
      loyalty_configs: Configuración de Puntos
      #ThirdPartyCodes
      third_party: Terceros
      third_party_codes: Códigos de Terceros
      #Users
      users: Usuarios
      shop_users: Administradores
      customers: Clientes
      customer: Cliente
      brands: Marcas
      dnis: DNIs
      users-pioneer: Usuarios de Pioneer
      roles: Roles
      offices: Sucursales
      #Payment
      payment: Métodos de Pago
      banks: Bancos
      bines: Bines
      modo: Modo
      loan_bna: Préstamos BNA
      programs: Programas
      #Accounting
      accounting: Facturación
      invoices: Facturas
      #Reports
      reports: Reportes
      cancelled: Órdenes canceladas
      url_generator: Generador de URLs
      #tdd
      tdd: Tarjeta del deporte
      members: Socios
      payments: Pagos
      plans: Planes
      exports: Exportaciones
      imports: Importaciones
      # invoice management
      invoice_management: Administración de facturas
      invoice_items: Operaciones
      supplier_invoices: Analíticos de facturación
      invoice_reports: Reportes guardados
      suppliers: Proveedores
      #Development tools
      dev-tools: Desarrolladores
      mails: Plantillas de Mail
      reservations: Reservas
      mimoto: Mi Moto
      renaper: Blacklist validación
      answers: Respuestas
      blacklists: Blacklists
      identity_validation_blocks: Bloqueos de Identidad
      #other
      curation: Contenido Curado
      landings: Páginas
      seo: SEO
      submenu:
        accounting:
          invoicing: Órdenes a facturar
          reports: Órdenes No Facturadas
          credit_notes: Notas de Crédito
    shop:
      account_executive: Comercial a cargo
      add-store: Agregar Tienda
      admin-title: Administración del Proveedor
      admin-owner: Dueño del Proveedor
      admin-admin: Administrador del Proveedor
      admin-change: Gestionar administradores
      admin-no-owner: Este proveedor no tiene dueño
      admin-no-owner-admin: No puedes gestionar los administradores de este proveedor por que el mismo no cuenta con un dueño #You can't change the administrators of this shop because it doesn't have an owner.
      admin-no-admin: Este proveedor no tiene administradores
      average: Promedio
      business_number: Número de Comercio %{business}
      cards: Tarjetas
      company-name: Razón Social
      contact_email: Email %{email}
      contact_number: Número de Contacto %{contact_type}
      contact_numbers: Números de Contacto
      create: Crear
      create-shop: Crear Proveedor
      created: Creado
      drop-shipping: A Cargo del Proveedor
      emails: Correos electrónicos
      export: Exportar
      fullfiled-by-gp: A cargo de Avenida
      fullfilled: A cargo de Avenida
      fullfilment: Tipo de Envío
      monthly-fee: Costo Mensual
      decidir-configuration: Configuración Decidir
      mercadolibre-configuration: Configuración Mercadolibre
      new-decidir-id: ID Sitio
      new-decidir-percentage: Porcentaje
      new-decidir-public-key: API key Pública
      new-decidir-private-key: API key Privada
      new-mercadolibre-app-id: App ID
      new-mercadolibre-secret-key: Secret Key
      new-email: Email
      new-fc: Consumidor Final
      new-matrix: Envío Con Matriz
      new-monthly-fee: Comisión mensual
      new-sale-commision: Comisión de venta
      new-slug: Alias
      new-title: Título
      new-reminder: Email recordatorio
      new-stores: Tiendas
      payment-program: Programa de pagos
      shipment-strategy: Estrategía de envíos
      payment-program-label: Programa
      new-visa-points: Configuración Visa puntos
      new-visa-equivalence: Visa Puntos (1 punto = x ARS)
      new-submit-update: Actualizar
      new-submit-create: Crear
      new-success: Proveedor creado con éxito
      non-visible: Proveedor No Visible
      not-visible: No Visible
      sale-commision: Comisión de venta
      search: Buscar
      searcher: Buscador
      title: Proveedores
      visibility: Visibilidad
      visible: Visible
      products: Productos
      remove-store: eliminar tienda
      total: Total
      update: Guardar
      update-error: Error al actualizar proveedor
      update-success: Proveedor actualizado con éxito
      view-title: Proveedor
      view-date: Fechade creación
      view-products: Productos
      view-view-products: Ver Productos
      view-average-price: Precio Promedio
      view-total-price: Precio Total
      shipment-strategies:
        suborder: Un envío por suborden
        product: Un envío por producto
      allows_pickup: Permite retiro por sucursal
      order_visibility: Orden visibilidad (Mayor número aparecerán primero)
      cuit: CUIT
      external_shop_id: Seller ID(NS)
      account_number_title: Cuenta Bancaria (préstamos)
      account_number: Número de cuenta corriente
    label:
      title: Configuración de Oca EPAK
      no-oca: Sin Oca EPAK
      door-door: Servicio puerta a puerta
      storse-door: Servicio Local a puerta
      #addmission-center: Centro de admisión
      #contact-name: Nombre de contacto
      submit: Guardar
    integrations:
      title: Integraciones
      integrated-shops: Proveedores integrados
      integrated-products: Productos integrados
      shop-name: Proveedor
      integration-date: Fecha de Integración
      type: Tipo
      products-quantity: Cantidad de productos
      variants-quantity: Cantidad de variantes
      product-name: Producto
      product-shop: Proveedor
      product-category: Categoría
      product-gender: Genero
      product-price: Precio
      product-sale-price: Oferta
      product-created-at: Creado
      product-updated-at: Actualizado
      product-stock: Disponibles
      variants_without_stock: 'Las siguientes variantes no fueron importadas por falta de stock'
      attributes_combination: 'Las siguientes variantes no fueron importadas por no tener una combinación válida de atributos'
      category_without_variations: 'La categoria predecida para los siguientes productos no permite variants'
      errors:
        category_without_variations: 'No pudo ser integrado'
    stores:
      allow-orders-cancellation: Permitir cancelar órdenes
      title: Tiendas
      create-store: Crear Tienda
      store-name: Nombre de la tienda
      store-token: Token
      general-info: Información general
      payment-methods: Medios de pago
      integrations: Integraciones
      installments: Cuotas
      installments-remove: Eliminar cuotas
      whitelist: Whitelist
      name: Nombre Interno
      header: Encabezado
      logo-url: URL del logo
      gateways-selection: Procesadores de pago habilitados
      use-bines: Utiliza bines
      use-alternative-payment: Intentar utilizar medio de pago alternativo
      no-bines-installments: Cuotas para procesadores de pago sin bines
      store-url: URL de la tienda
      newsletter-image: URL de la imagen del newsletter
      email: Email de contacto
      telephone: Teléfono de contacto
      cash-discount: Descuento de pago al contado
      image-dues: Image dues
      preferred_sorting: Orden del catálogo
      limit_amount: Monto límite
      points-equivalence-title: Puntos Avenida
      points-equivalence: Equivalencia de puntos
      social-media: Redes Sociales
      configuration: Configuración
      is-active: Tienda Activada
      invoices: Emite factura
      is-free-shipping: Utilizar envío gratuito Avenida.com
      abandoned-cart: Enviar email por carro abandonado
      free-shipping: Envíos gratis
      cancelation-service-configuration: Configuración de cancelación de ordenes
      generate-refund-shipments: Generar envíos por devolución
      generate-coupons: Generar cupones
      generate-credit-notes: Generar notas de crédito
      cancel-payments: Cancela pagos
      from: Desde
      to: Hasta
      amount: Monto
      active: Activo
      decidir-credentials: Credenciales de Decidir
      payments-credentials: Credenciales de Payments (Módulo de Avenida+)
      public-key: KEY publica
      private-key: KEY privada
      avenida-distributed-site-id: ID Sitio avenida distribuido
      mercadopago-credentials: Credenciales de MercadoPago
      client-id: Id del cliente
      client-secret: Secreto del cliente
      access-token: Token de acceso
      first-data-credentials: Credenciales First-Data
      store-id: ID de la tienda
      user: Usuario
      password: Contraseña
      password-cert: Contraseña del certificado
      product-approval: Aprobación de productos
      ssl-cert: Certificado SSL
      ssl-cert-key: KEY del Certificado SSL
      visa-points: Configuración Visa Puntos
      visa-points-api-key: API KEY
      visa-points-equivalence: Visa Puntos(1 punto = x ARS)
      visa-points-equivalence-sube: Visa Puntos SUBE (1 punto = x ARS)
      visa-points-equivalence-cellphone: Visa Puntos Recarga Celulares (1 punto = x ARS)
      aero-arg: Credenciales de Aerolineas Argentinas
      aero-arg-user: Usuario Aerolineas Argentinas
      aero-arg-password: Contraseña
      aero-arg-equivalence: Equivalencia (1 punto = x Millas)
      aero-arg-partner-code: Codigo del sócio
      aero-arg-partner-id: ID del sócio
      aero-arg-partner-nbr: NBR del sócio
      w-is-active: Activar whitelist
      w-validation: Estrategia de validación
      w-user: Estrategia de usuario
      w-unauthorized: Mensaje de usuario no autorizado
      integration: Configuración de Mercadolibre
      allow_meli_integration: Permitir Integración de Meli
      renaper: Renaper
      renaper-is-active: Activar preguntas de Renaper
      w-insurance: Estrategia de seguros
      insurance_types:
        insurance_configuration_table: "InsuranceConfiguration"
        pioneer: "Backend (Pioneer)"
        ochenta_grados: "80 Grados"
      coati-enabled: Módulo logístico Coatí
    carrier_stores:
      import: Importar .csv
      export: Exportar .csv
      clone: Duplicar Matriz
      create: Crear Matriz
      delete: Borrar todo
      delete_by_shop: Borrar proveedor
      last-update: Ultima Actualización
      shop-name: Proveedor
      transport: Transporte
      zip-from: C.P. Desde
      zip-to: C.P Hasta
      weight-from: Peso mínimo
      weight-to: Peso máximo
      message: Mensaje
      price: Precio
      operative: Operativa
      actions: Acciones
      edit: Editar
      delivery_message: Mensaje sobre el envio
      shop-select: Proveedor (Blanco si no aplica)
      store: Tienda
      clone-alert: 'Selecciona el store al cual se va a copiar la matriz que tenemos en %{title}, Nota: recuerda si el store seleccionado tiene datos de matriz esta perdera los datos. %{title} NO RECIBIRA NINGÚN CAMBIO.'
      delete_by_shop_alert: 'Selecciona el proveedor que será eliminado de la matriz asociada a %{title}, recuerda que se perderan los datos en matriz asociados al proveedor seleccionado.'
      delete_by_shop_title: 'Eliminar proveedor de la matriz'
      delete_by_shop_success: 'Se han eliminado %{count} elementos exitosamente.'
      delete_by_shop_not_delete: 'No se han eliminado registros. El proveedor seleccionado no se encuentra en la matriz.'
      update: Actualizar
    categories:
      title: Categorías
      export: Exportar .csv
      masive_import: Carga Masiva
      commission: Comisión
      create: Crear categoría
      active: Categoría activa
      stores: Tiendas
      name: Nombre
      description: Descripción
      weight: Peso
      height: Altura
      length: Largo
      width: Ancho
      save: Guardar
      length-unit: Unidad de medida
      mass-unit: Unidad de peso
      products: Productos
      products-stock: Productos con disponibilidad
      pickeable: Retiro por sucursal
      cat-active: Categorías activas
      cat-sub-active: Sub-categorías activas
      cat-inactive: Categorías inactivas
      cat-sub-inactive: Sub-categorías inactivas
      cat-main: Crear categoría principal
      cat-sub: Crear sub-categoria de
      cat-edit: Editar categoría
      cat-default: Valores por defecto
      cat-visa-points: Configuración de Visa Puntos
      cat-visa-equivalence: Visa puntos (1 punto = x ARS)
      cat-store-banner: "Banner para tienda %{store}"
      cat-max-installment: Máximas cuotas
      cat-title: Título
      cat-id: ID
      cat-shop: Proveedor
      cat-stock: Disponible
      visibility-error: No es posible editar la visibilidad de esta categoría
      insurance_coef: Coeficiente de Seguro
      insurance_start_date: Desde
      insurance_end_date: Hasta
    orders:
      aditional-comment: Comentario adicional
      amount: Monto
      approved: Aprobado
      available: En proceso
      billed: Facturado
      booked: Reservado
      overdue: Vencido
      cancel-accept: Aceptar
      cancel-close: Cerrar
      cancel-order: Cancelar orden
      cancel-order-title: Orden a cancelar número
      state-change-order: Cambiar estado orden
      documents: Documentos
      cancel-reason: Motivo
      cancel-suborder-title: Suborden a cancelar número
      cancel-type: Típo
      cancelled: Cancelado
      card-number: Número de tarjeta
      client: Cliente
      collected: Aprobado
      coupons: Cupones
      coupons-bonified: Bonificado
      coupons-id: Cupon
      coupons-promotion: Promoción
      customer-deleted: El cliente fue eliminado
      date: Fecha
      declined: No Aprobado
      delivered: Entregado
      delivery-error: Ocurrió un error con la creación de la orden
      delivery-external-redemption-failed: Ocurrió un error al informar la venta al proveedor
      delivery-fullfiled: El pedido esta siendo cumplido por Avenida.com
      delivery-id: El identificador del envío es
      delivery-pickup: Pedido por método de retiro por sucursal
      edit-accept: Aceptar
      edit-address: Dirección
      edit-address-2: Piso / Depto
      edit-close: Cerrar
      edit-email: E-mail
      edit-number: Número
      edit-reason: Motivo
      edit-title: Orden a editar número
      exchange: Cambio
      exchange-order-title: Orden a cambiar número
      exchange-suborder-title: Suborden a cambiar número
      exchange-aclaration: El cambio solo se realiza por exactamente el mismo producto
      exchange-reason: Motivo
      exchange-type: Tipo
      exchange-close: Cerrar
      exchange-accept: Aceptar
      export: Exportar CSV
      expired: Expirado
      filter: Filtrar
      filter-creation-date: Fecha de creación
      filter-from: Desde
      filter-payment-date: Fecha de Pago
      filter-payment-status: Estado del pago
      filter-search: Buscar
      fitler-search-by: Buscar por id, nombre, apellido o email
      filter-shipping-status: Estado del envío
      filter-to: Hasta
      frecuent-passenger: Pasajero frecuente
      fulfilled: Entregado
      id: ID
      in_process: En proceso
      miles: Millas
      none: Ninguno
      not_delivered: No Entregado
      order-title: Título
      pending: Pendiente
      payment-amount: Monto
      payment-coupon: Esta orden fue pagada con cupón
      payment-coupon-amount: '%{discount} de %{total} fue abonado con cupón'
      payment-date: Fecha de acreditación
      payment-error: No hay un pago registrado para esta orden
      payment-id: '# '
      payment-modal-id: "Id de Payment: %{payment_id}"
      payment-installments: Cuotas
      payment-points: Puntos
      payment-method: Método de pago
      payment-status: Estado
      payment-card-name: Titular del Pago
      payment-dni-card: DNI Tarjeta
      payment-4-digits: Ultimos Dígitos Tarjeta
      phone-number: Número de teléfono
      pending_cancellation: Pendiente a cancelacion
      points: Puntos
      posted: Contabilizado
      purchase: Compra
      purchase-aditional: Detalles adicionales
      purchase-details: Detalles de la compra
      purchase-reference: Referencia
      purchase-title: Producto
      quantity: Cantidad
      refound-order-title: Orden a devolver número
      refound-suborder-title: Suborden a devolver número
      refound-reason: Motivo
      refound-type: Tipo
      refound-close: Cerrar
      refound-accept: Aceptar
      refund-order: Devolver orden
      refunded: Reintegrado
      returned: Devuelto
      select-one: Seleccione...
      shipped: Enviado
      shipping-address: Dirección
      shipping-bonified: Bonificación
      shipping-cost: Costo
      shipping-courrier: Logística
      shipping-id: '#'
      shipping-method: Método de Envío
      shipping-status: Estado
      shipping-status-date: Última actualización
      shipping-tracking: Seguimiento
      shipping-type: Tipo
      store: Tienda
      suborder-details: Detalles de las sub-órdenes
      suborder-number: '#'
      suborder-shop: Proveedor
      suborder-status: Estado
      suborder-taxes: Impuestos
      suborder-title: Título
      suborder-quantity: Cantidad
      success_title: "Actualización exitosa"
      success_message: "El estado del pago ha sido actualizado correctamente."
      close: "Cerrar"
      title: Órdenes
      total: Total
      undo-changes: Deshacer cambios
      unfulfilled: Sin despachar
      ready_to_pick_up: Listo para Retirar
      #shipment kind
      refund: Devolución
      exchange_refund: Cambio
      exchange_change: Cambio
      delivery: Envío
      virtual: Normal
    carts:
      select-option: Seleccione...
      filter: Filtrar
    products:
      actions: Acciones
      approved: Aprobado
      approve-selected: Aprobar seleccionados
      origin: Orígen
      category: Categoría
      second_category: Segunda Categoría
      created-at: Creado
      export-all: Exportar todo
      filter: Filtrar
      no-variants: Este producto no tiene vairantes
      image: Imagenes
      list-export: Lista de Exportaciones
      manufacturer: Fabricante
      not-visible: No Visible
      pending: Pendiente
      points: Puntos
      products: Productos
      price: Precio
      reject-selected: Rechazar seleccionados
      rejected: Rechazado
      sale-from: Oferta desde
      sale-price: Oferta
      sale-to: Oferta hasta
      search: Buscar
      search-by: Buscar por título, sku o descripción
      select-option: Seleccione...
      shop: Proveedor
      sku: SKU
      sold: Vendido
      status: Estado
      stock: Disponible
      store: Tienda
      title: Título
      variants: Variantes
      visa-puntos: Visa puntos
      visible: Visible
      order-visibility-selected: Cambiar visibilidad seleccionados
      order-visibility: Orden Visibilidad
    origin-of-products:
      assembled_in_argentina: Ensamblado en Argentina
      national: Nacional
      imported: Importado
    pending-changes:
      regular_price: Precio
      category_id: Id de Categoría
      second_category_id: Id de 2º Categoría
      sale_price: Precio de oferta
      origin_of_product: Orígen del producto
      available_on: Visible desde
    cucardas:
      config: Configuración Cucardas
      title: Título Cucardas
      active: Cucarda activa
      select: Selecciona una cucarda
      form:
        descripcion: Descripción
        edit: Modificar
        image: Imagen
        name: Nombre
        new: Crear
      index:
        buttons_back: Volver al listado
        confirm_delete: Esta seguro que desea eliminar?
        delete: Eliminar
        edit: Editar
        image: Imagen
        name: Nombre
        new_cucarda: Nueva Cucarda
        no_cucardas_found: Sin cucardas
        save_table: Guardar Cucardas
        search_button: Buscar
        search_placeholder: Nombre o Descripción
        title: Cucardas
      edit:
        buttons_back: Volver al listado
        title: Editar Cucarda
      new:
        buttons_back: Volver al listado
        title: Nueva Cucarda
      show:
        buttons_back: Volver al listado
        delete: Eliminar
        descripcion: Descripción
        edit: Editar
        image: Imagen
        name: Nombre
        title: Ver Cucarda
    loyalty_configs:
      config: Configuración de puntos
      title: Título Configuración de puntos
      active: Configuración activa
      select: Selecciona una configuración
      form:
        descripcion: Descripción
        edit: Modificar
        image: Imagen
        name: Nombre
        new: Crear
      index:
        buttons_back: Volver al listado
        confirm_delete: Esta seguro que desea eliminar?
        delete: Eliminar
        edit: Editar
        image: Imagen
        name: Nombre
        save_table: Guardar configuración
        search_button: Buscar
        search_placeholder: Nombre o Descripción
        title: Configuración
        last_12_months: Últimos 12 meses
        month_data: Datos mensuales
        month: Mes
        total_points: Total de puntos
        transaction_count: Cantidad de transacciones
        total_amount: Monto total en pesos
      edit:
        buttons_back: Volver al listado
        title: Editar configuración
      new:
        buttons_back: Volver al listado
        title: Nueva configuración
      show:
        buttons_back: Volver al listado
        delete: Eliminar
        descripcion: Descripción
        edit: Editar
        image: Imagen
        name: Nombre
        title: Ver configuración
    partials:
      billing-title: 'Datos de facturación:'
      location: 'Localidad: '
      billing_address: 'Dirección'
      cart_item:
        noproperty: Única variante
      customer-name: Nombre
      customer-razon_social: Razón Social
      customer-email: E-mail
      customer-telephone: Teléfono
      customer-doc_number: CUIT
      item-image: Imagen
      customer-cuil: CUIL
      item-title: Título
      item-quantity: Cantidad
      item-color: Color
      item-sale-price: Precio de Oferta
      item-price: Precio regular
      item-variant-deleted: La variante fue eliminada
      item-no-products: No hay productos para esta orden, posiblemente fueron eliminados
      item-properties: Propiedades
      item-status: Estado
      item-last-shipment-related: Último envío asociado
      selectable_item:
        articulo: Categoria articulo Id
        color: Color
        dimensions: Dimensiones
        external: External Id
        hardness: Propiedad uno
        length: Propiedad dos
        material: Propiedad tres
        size: Talle
        percentage: Porcentaje
        payment_method: Método de pago
        noproperty: Variante única
        period: Periodo de Garantia
      item:
        articulo: Categoria articulo Id
        color: Color
        dimensions: Dimensiones
        external: External Id
        hardness: Propiedad uno
        length: Propiedad dos
        material: Propiedad tres
        size: Talle
        percentage: Porcentaje
        payment_method: Método de pago
        noproperty: Variante única
        period: Periodo de Garantia
    admin_users:
      are_you_sure: ¿Esta seguro?
      confirm_password: Confirmar contraseña
      create_user: Crear usuario
      delete_user: Eliminar usuario
      email: Email
      password: Contraseña
      permission: '%{role} in %{store}'
      result: resultado
      results: resultados
      role: Rol
      search: Buscar
      search_by: Buscar por email o rol
      update_user: Actualizar usuario
      users: Usuarios
    bines:
      title: Bines
      filter-store: Filtrar Tienda
      create: Crear Bin
      store: Tienda
      bank: Banco Emisor
      brand: Bandera
      number: Número
      installments: Cuotas
      from: Desde
      to: Hasta
      edit-bin: Editar Bin
      back: Atras
      tna: TNA
      cft_tna: CFT TNA
      coef_tna: COEF TNA
      tea: TEA
      cft_tea: CFT TEA
      coef_tea: COEF TEA
      search-category: Buscar Categoría
      category: Categoría
      product: Producto
      installments-remove: Remover
      installments-create: Crear Cuotas
      installments-available: Cuotas disponibles
      update: Actualizar
      purchase-total-limit: Límite de compra
      first-purchase-only: Solo primera compra
      valid-for: Valido para
      gateway: Gateway
      exclusive_gateway: Gateway (Exclusivo)
      updated: Bin actualizado
    payment_programs:
      title: Programas
      edit: Editar Programa
      create: Crear Programa
      name: Nombre
      installments: Cuotas
      edit-bin: Editar Programa
      back: Atras
      tna: TNA
      cft_tna: CFT TNA
      coef_tna: COEF TNA
      tea: TEA
      cft_tea: CFT TEA
      coef_tea: COEF TEA
      installments-remove: Remover
      installments-create: Crear Cuotas
      installments-available: Cuotas disponibles
      update: Actualizar
      external_program_id: Id externo del programa
      submit: Buscar
    third_party_codes:
      form:
        code: Código
        manufacturer: Tienda
        new: Crear
        edit: Modificar
      index:
        title: Códigos de Terceros
        used: Canjeado?
        code: Código
        store: Tienda
        available: Disponible?
        search: Buscar
        options: Acciones
        new: Nuevo Código de Tercero
        confirm_delete: Esta seguro que desea eliminar?
        edit: Editar
        delete: Eliminar
      edit:
        title: Editar Código de Tercero
        used: Canjeado?
        available: Disponible?
      new:
        title: Nuevo Código de Tercero
    dnis:
      form:
        number: Numero
        new: Crear
        edit: Modificar
      index:
        title: Dnis
        number: Numero
        search: Buscar
        options: Acciones
        new_dni: Nuevo Dni
        confirm_delete: Esta seguro que desea eliminar?
        edit: Editar
        delete: Eliminar
      edit:
        title: Editar Dni
      new:
        title: Nuevo Dni
    tdd:
      services:
        form:
          name: Nombre
          amount: Monto
          points: Puntos
          new: Crear
          edit: Modificar
        index:
          title: Planes
          name: Nombre
          amount: Monto
          points: Puntos
          options: Acciones
          new_service: Nuevo Plan
          confirm_delete: Esta seguro que desea eliminar?
          edit: Editar
          delete: Eliminar
        edit:
          title: Editar Plan
        new:
          title: Nuevo Plan
      exports:
        index:
          title: Exportaciones
          create_export: Crear Export
        list:
          card: Tarjeta
          card_type: Tipo
          date: Fecha
          period: Periodo
          registry_amount: Cant. Registros
          total: Total
          options: Acciones
          see_file: Ver Archivo
          create_export_title: Crear Pago Recurrente
          create_export: Crear Export
      payments:
        index:
          title: Pagos
          payment_report_button: Crear Reporte
        list:
          number_member: N° Socio
          email: Email
          dni: Dni
          status: Estado
          period: Periodo
          options: Acciones
        payment_info:
          list_card_brand: 'Tarjeta: '
          list_card_type: 'Tipo: '
          list_billable: 'Facturable: '
          list_error: 'Error: '
          list_invoiced_date: 'Fecha de Facturacion: '
          list_invoice_number: 'Factura: '
          list_amount: 'Monto: '
          list_created_at: 'Fecha de Creación: '
          list_recurrent: 'Recurrente: '
      members:
        index:
          title: Socios
        list:
          email: Email
          full_name: Nombre completo
          dni: Dni
          origin: Origen
          created_at: Fecha Alta
          amount: Precio
          active: Activo
          options: Opciones
      tdd_reports:
        header:
          title: TDD Reports
          export_button: Exportaciones
          import_button: Importaciones
        index:
          subtitle: Importación del archivo de salida, del ente recaudador.
          card: Tarjeta
          card_type: Tipo
          date: Fecha
          period: Periodo
          amount: Cant.
          total_amount: Monto Total
          options: "-"
          see_button_label: VER
        show:
          period: Periodo
          payments_amount: Cant. de Pagos
          total_amount: Monto Total
          dni: DNI Miembro
        exports:
          subtitle: Exportacion del archivo para traspasar al ente recaudador.
          card: Tarjeta
          card_type: Tipo
          date: Fecha
          period: Periodo
          amount: Cant.
          options: "-"
          see_button_label: VER
          file_button_label: Archivo
        imports:
          subtitle: Importación del archivo de salida, del ente recaudador.
          card: Tarjeta
          card_type: Tipo
          date: Fecha
          period: Periodo
          amount: Cant.
          total_amount: Monto Total
          options: "-"
          see_button_label: VER
      imports:
        index:
          title: Importaciones
        list:
          payment_import: Importar Pago
          card: Tarjeta
          card_type: Tipo
          date: Fecha
          period: Periodo
          collected_payments: P. Colectados
          options: Acciones
          file_button_label: Ver Archivo
      invoices:
        new:
          title: Nueva Factura
          number: Numero
          new: Crear
          edit: Modificar
    dashboard:
      title: Dashboard
      filter: Filtrar
      filter-search: Buscar
    supplier-invoices:
      order-id: ID Orden/Suborden
      transaction-id: ID Transacción
      store: Tienda
      title: Facturas de proveedores
      filter: Filtrar
      supplier: Proveedor
      shop: Proveedor
      invoice_status: Estado
      from-to: Desde/Hasta
      invoice-number: Nro. de Factura
      order-number: Nro. de Pedido
      order_status: Estado de la Orden
      points_equivalence: Equivalencia puntos-pesos
      #show
      external_id_title: "Nro. HES: %{external_id}"
      external_date_title: "Fecha HES: %{external_date}"
      invoice_number_title: "Nro. de Factura: %{invoice_number}"
      order_number_title: "Nro. de Pedido: %{order_number}"
      #new
      new_title: Analítico de Facturación
      new_store_title: "Tienda: %{store_name}"
      new_shop_title: "Proveedor: %{shop_name}"
      new_creation_date: "Fecha: %{date}"
      order_id: Id
      created_at: Creado el
      fulfilled_at: Fecha estado final
      amount: Pesos
      points: Puntos
      #update
      external_id: nro. HES
      external_date: Fecha HES
      #actions
      voic-selected-invoice: Anular factura
      id: Id
      #shipment statues
      unfulfilled: Sin despachar
      shipped: Enviado
      in_process: En proceso
      delivered: Entregado
      not_delivered: No Entregado
      returned: Devuelto
      cancelled: Cancelado
      #invoice status
      pending: Pendiente
      invoiced: Facturado
      #filter labels
      select-one: Seleccione ...
      filter-creation-date: Fecha de creación
      filter-from: Desde
      filter-to: Hasta
      filter-search: Buscar
      fitler-search-by: Buscar por HES, Analítico, Nro de Facturación o Pedido
      filter-invoice-status: Estado de facturación
      filter-clean-filters: Limpiar filtros
    suppliers:
      title: Proveedores
      name: Nombre
      new: nuevo
      slug: Alias
      shop: Proveedor
      manage-stock: Administra Stock?
      new-title: Nuevo proveedor
      edit-title: Editar proveedor
      supplier-true: Sí
      supplier-false: No
      select-one: Seleccione ...
      unit: Unidad
      store: Tienda
    supplier-stocks:
      title: Adminstración de Stock
      new-title: "Agregar %{unit} para %{supplier_name}"
      add-stock: "Agregar Stock"
    invoice_items:
      store: Tienda
      title: Operaciones
      invoices: Facturas
      supplier: Proveedor
      shop: Proveedor
      #actions
      new-invoice-selected: Facturar seleccionados
      new-invoice-all: Facturar
      export: Exportar
      recently-exports: Listado de exportaciones
      #index table
      order_id: ID
      transaction_id: ID transacción VASA
      created_at: Creado el
      order_status: Estado de la Orden
      fulfilled_at: Fecha estado final
      shipment_status: Estado del Envío
      invoice_status: Estado de Facturación
      amount: Monto pesos
      points: Total puntos
      #filter labels
      select-one: Seleccione ...
      filter: Filtrar
      filter-creation-date: Fecha de creación
      filter-from: Desde
      filter-to: Hasta
      filter-fulfilled-date: Fecha de Entregado
      filter-order-status: Estado de la Orden
      filter-invoice-status: Estado de Facturación
      filter-search: Buscar
      fitler-search-by: Buscar por nro orden o id de transacción
      filter-shipping-status: Estado del envío
      filter-clean-filters: Limpiar filtros
      filter-save-filters: Guardar filtros
      filter-saved-filters: Filtros Guardados
      dashboard-work-with: Ver operaciones
      #shipment statues
      unfulfilled: Sin despachar
      shipped: Enviado
      in_process: En proceso
      delivered: Entregado
      not_delivered: No Entregado
      returned: Devuelto
      cancelled: Cancelado
      # invoice status
      pending: Pendiente
      invoiced: Facturado
      # order status
      exchange: Cambio
      fulfilled: Entregado
    invoice-reports:
      title: Reportes guardados
      name: Nombre
      description: Descripción
      parameters: Parametros
      new-title: Nuevo Reporte
      update-title: Actualizar Reporte
      store: Tienda
      shop: Proveedor
      creation-date: Creado el
      fulfilled-date: Entregado el
      from: Desde
      to: Hasta
      shipping-status: Estado de Envío
      invoice-status: Estado de Facturación
      search: Buscar por
      #shipment statues
      unfulfilled: Sin despachar
      shipped: Enviado
      in_process: En proceso
      delivered: Entregado
      not_delivered: No Entregado
      returned: Devuelto
      cancelled: Cancelado
    bin:
      number: Un store solo puede tener un numero de Bin
    v2:
      orders:
        partials:
          destination_address:
            title: Datos para el envío
            pickup: (el comprador pasa a retirarlo)
            doc_numer: "DNI: %{number}"
            phone: "Teléfono: %{number}"
          filter:
            created_between: 'Creada entre:'
            and: 'y'
            payment_status: 'Pago/s:'
            query: 'Buscar por:'
            query_placeholder: 'Busca por ID, o por titulo'
            shipment_status: 'Envio/s:'
            submit: 'Buscar y/o Filtrar'
          items:
            properties:
              color: 'Color: '
              dimensions: 'Dimensiones: '
              hardness: 'Dureza: '
              length: 'Largo: '
              material: 'Material: '
              noproperty: 'Sin variante:   '
              size: 'Talle: '
              percentage: 'Porcentaje de descuento: '
              payment_method: 'Método de pago: '
            manufacturer: 'Marca: %{name}'
            shop: 'Merchant: %{name}'
            quantity_html: 'Cant. <strong>%{number}</strong>'
          label:
            cancel: 'Cancelar Envío'
            estimated_delivery_date_html: 'Fecha estimada de entrega: <strong>%{date}</strong>'
            print_label: 'Etiqueta para el paquete (PDF)'
            shipping_documents: 'Descargá e Imprimí estos documentos para realizar el envío: '
            tracking_url: 'URL para el tracking'
          payments:
            events:
              status:
                approved: 'Aprobado:'
                cancelled: 'Cancelado:'
                charged_back: 'Contracargo en la Tarjeta de crédito:'
                in_mediation: 'En disputa:'
                in_process: 'En revisión:'
                refunded: 'Devuelto:'
                rejected: 'Rechazado (el usuario puede intentarlo nuevamente):'
                pending: 'Pendiente:'
              status_detail:
                accredited: 'Listo, se acreditó el pago.'
                by_collector: 'Por nosotros'
                by_payer: 'Por el comprador'
                cc_rejected_high_risk: 'Rechazado por la Tarjeta de Credito. Comprador con riesgo alto.'
                cc_rejected_other_reason: 'El banco no proceso el pago. No tenemos detalles.'
                expired: 'Expiró.'
                in_process: 'En proceso. Seguí este tema desde el sitio de Mercadopago para evitar el contracargo.'
                partially_refunded: 'Devuelto parcialmente.'
                payer_unavailable: 'No se pudo verificar al comprador (No disponible)'
                pending: 'Pendiente - En progreso'
                pending_contingency: 'Estamos procesando el pago. En menos de una hora te enviaremos por e-mail el resultado.'
                pending_review_manual: 'Estamos procesando el pago. En menos de 2 días hábiles te diremos por e-mail si se acreditó o si necesitamos más información.'
                pending_waiting_payment: 'Esperando a que el cliente realize el pago.'
                refunded: Devuelto al comprador
                reimbursed: Reembolsado al comprador
              amount_html: "$ <strong>%{amount}</strong>"
            gateway_url_html: 'ID en %{name}: <strong>#%{id}</strong> - Clickeá para ver el detalle'
            id_html: "ID: #<strong>%{id}</strong>"
            installments_html: "Cuotas: <strong>%{installments}</strong>"
            methods:
              cash_html: "Medio de pago: <strong>Efectivo</strong>"
              credit_card_html: "Medio de pago: <strong>Tarjeta de Crédito</strong>"
              ticket_html: "Medio de pago: <strong>Cupón de pago</strong>"
              atm_html: "Medio de pago: <strong>ATM / Cajero</strong>"
            order_total_html: "Total a cobrar: <strong>$ %{total_amount}</strong>"
            status:
              pending: Pendiente
              collected: Cobrado
              cancelled: Cancelado
              refunded: Devuelto
              fully_paid_via_coupon_html: 'Pagó con cupón de descuento: <i>%{coupon}</i>'
            title: Pagos
          shipment:
            title: 'Envío #%{id}'
            unfulfilled_title: 'Items a enviar (Cantidad: %{quantity})'
            fulfilled_by_you_html: 'El <strong>%{date}</strong> iniciaste este envío'
            fulfilled_by_gateway_html: 'El <strong>%{date}</strong> iniciaste este envío con <strong>%{gateway}</strong>'
            fulfilled_by_merchant_html: '<strong>%{merchant}</strong> se hizo cargo de este envío el <strong>%{date}</strong>'
            status:
              unfulfilled: --
              in_process: Solicitado
              shipped: En camino
              delivered: Entregado
              not_delivered: No Entregado
              cancelled: Cancelado
          shipment_gateway:
            cancel: Cancelar
            comment_html: '<strong>Comentario</strong>: %{comment}'
            on_hold: Pausar
            shipping_options_html: '<strong>Servicio a utilizar</strong>: %{carrier} - <i>%{service}</i>'
            shipment_url_html: '<strong>#%{id}</strong> - Clickeá para ver el envío en <strong>%{name}</strong>'
          unfulfilled:
            title: 'Items a enviar (Cantidad: %{quantity})'
            fulfill_items_with_shipnow: 'Procesar con ShipNow'
            fulfill_items: 'Realizar Envío'
            awaiting_payment: 'Estamos esperando la confirmación del pago, para que puedas procesar este envío'
            voided_payment: 'El pago está cancelado o devuelto por lo que no debes procesar este envío.'
        fulfillment:
          head:
            title: Envíos
          header:
            title: Órdenes para procesar y enviar (%{count})
          export_csv: Exportar en CSV
          export_limit: Tiene un limite de 1000 registros
        show:
          back_to_fulfillment: Volver a las órdenes
          head:
            title: "Envío: %{title}"
          header:
            title_html: "<span class='thin'>Envío de subordenes:</span> %{title}"
          order_placed: 'Compra realizada el '
          other_items: 'Otros productos relacionados con esta orden'
          other_items_explain_html: 'Estos productos pertenencen a los <i>"dropshippers"</i>, que son aquellos <strong>Merchants</strong> que se encargan de realizar el packing y el envio por su cuenta.'
      accounting:
        credit_notes:
          index:
            title: Notas de Credito
            header: "Notas de Credito Generadas: %{count}"
        invoicing:
          title: Órdenes a Facturar
          without_invoices: "Órdenes sin facturar: %{count}"
          from: 'Desde'
          to: 'Hasta'
          query: 'Buscar'
          query_placeholder: 'Busca por ID, o por titulo'
          submit: 'Buscar'
          invoiced: 'Facturado'
          store: Tienda
        not_invoiced_orders:
          index:
            title: Órdenes No Facturadas
          partials:
            filter:
              and: 'y'
              created_between: 'Creada entre:'
              query: 'Buscar por:'
              query_placeholder: 'Busca por ID, o por titulo'
              submit: 'Buscar y/o Filtrar'
              invoiced: 'Facturada'
      stores:
        partials:
          new:
            carriers:
              cp_from: CP desde
              cp_to: CP hasta
              delivery_message: Mensaje sobre el envio
              price: Precio
              weight_max: Peso Máximo
              weight_min: Peso Mínimo
    reports:
      amount: Total
      cancelled-orders: Órdenes canceladas
      cancelled-suborders: Subórdenes canceladas
      date: Fecha
      export-csv: Exportar CSV
      from: "Desde: "
      reports: Reportes
      select-option: Seleccione...
      store: Tienda
      to: "Hasta: "
      view-results: Ver resultados
      type: Tipo
    url_generator:
      url_import: Crear URLs
      succeded_creation: URLs creados con exito
      lables:
        file: Archivo
        name: Nombre
        last_name: Apellido
        cuit: Cuit
        office: Sucursal
        solicitude_id: ID Solicitud
        email: email
        amount: Monto preaprobado
        profile: Perfil
        program: Programa
        date: Fecha
        url_qty: Cantidad
