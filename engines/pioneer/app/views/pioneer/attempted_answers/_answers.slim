tr
  td = index
  td
    - if store_id.is_a?(Integer)
      = @stores_cache[store_id]&.name || store_id
    - else
      = store_id
  td = doc_number
  td = type
  td = counter
  td
    - begin
      - recent_attempts = @recent_attempts_cache[[doc_number, store_id]] || []
      - recent_24h_attempts = recent_attempts.select { |attempt| attempt.created_at >= 24.hours.ago }
      - if recent_24h_attempts.count >= 4
        span.text-danger Sí
      - else
        span.text-success No
    - rescue => e
      span.text-muted N/A
  td
    - begin
      - if @blacklist_cache.include?([doc_number, store_id])
        span.text-danger Sí
      - else
        span.text-success No
    - rescue => e
      span.text-muted N/A
  td = link_to(content_tag(:i, '', class: ['fa', 'fa-trash', 'px-2']), attempted_answer_path(doc_number), data: {confirm: t('pioneer.answers.are_you_sure')}, method: :delete, class: 'px-2', title: t('pioneer.answers.delete'))
