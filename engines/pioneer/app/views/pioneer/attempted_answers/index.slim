.panel.panel-default
  .panel-body
    .row.p-3
      .col-md-12
        h2.ml-5 = t('pioneer.answers.title')
    hr
    .mx-5
      = render partial: 'filters'

    .row.pt-5
      .col-md-12.table-responsive
        table.table id="accordion"
          thead
            tr
              th style="text-align: center" = t('pioneer.answers.id')
              th style="text-align: center" = t('pioneer.answers.tienda')
              th style="text-align: center" = t('pioneer.answers.customer_dni')
              th style="text-align: center" = t('pioneer.answers.type')
              th style="text-align: center" = t('pioneer.answers.attempts')
              th style="text-align: center" = t('pioneer.answers.temporary_block')
              th style="text-align: center" = t('pioneer.answers.permanent_block')
              th style="text-align: center" = t('pioneer.answers.actions')
          tbody align='center'
            - @attempted_answers.each_with_index do |(s, k), index|
              = render partial: 'answers', locals: {store_id: s[0], doc_number: s[1], type: s[2].split("::")[1], counter: k, index: index + 1}

    .row.mb-2.p-4
      .col-md-6.text-right
        = will_paginate(@attempted_answers)
