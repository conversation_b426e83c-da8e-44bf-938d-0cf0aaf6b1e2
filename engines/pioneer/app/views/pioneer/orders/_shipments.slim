table.table.mb-0.shipments
  thead
    th = t('pioneer.orders.shipping-id')
    th = t('pioneer.orders.shipping-status')
    th = t('pioneer.orders.shipping-status-date')
    th = t('pioneer.orders.shipping-type')
    th = t('pioneer.orders.shipping-courrier')
    th = t('pioneer.orders.shipping-tracking')
    th = t('pioneer.orders.shipping-cost')
    th = t('pioneer.orders.shipping-bonified')
    th = t('pioneer.orders.shipping-address')
  tbody
    - if defined?(shipments)
      - shipments.each do |shipment|
        tr.shipment
          td = shipment.id
          td
            span.status class="#{shipment.status}"
              = t("pioneer.orders.#{shipment.status}")
          td
            - if shipment.status_changes.any?
              = shipment.status_last_updated_at
            - else
              = shipment.created_at.strftime('%d-%m-%Y %H:%M')
          td
            = shipment.shipment_kind_label
          td
            - if shipment.courier_name.present?
              = shipment.courier_name.capitalize
            - else
              | -
          td
            - if shipment.has_labels?
              - if shipment.gateway_name == 'Coati'
                = link_to shipment.label.tracking_number, "#{COATI_TRACKING_URL}#{shipment.label.tracking_number}", target: "_blank"
              - else
                = link_to shipment.label.tracking_number, "#", 'data-toggle': 'modal', 'data-id': "#{shipment.label.id}", title: 'Show shipment details', class: 'show-shipment-details'
            - else
              | -
          td = number_to_currency(shipment.charged_amount, precision: 2)
          td = number_to_currency(shipment.bonified_amount, precision: 2)
          td
            - if shipment.shipment_kind_label == 'Pickup'
              = "#{shipment.origin_address.first_name} #{shipment.origin_address.last_name}"
              br = "#{shipment.origin_address.address} #{shipment.origin_address.address_2}"
              = "#{shipment.origin_address.city}, #{shipment.origin_address.state}. #{shipment.origin_address.zip}"
            - else
              = shipment.destination_address.full_name
              br = "#{shipment.destination_address.address} #{shipment.destination_address.address_2}"
              = "#{shipment.destination_address.city}, #{shipment.destination_address.state}. #{shipment.destination_address.zip}"
