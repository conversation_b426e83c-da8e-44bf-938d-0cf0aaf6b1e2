# Abstract controller to remove duplicated logic
module Pioneer
  class UsersController < Pioneer::ApplicationController
    protected

    def assign_as_admin(entity, user)
      if entity && !entity.can_be_managed_by?(user)
        entity.admins << user
        entity.save!
      end
    end

    def create_manufacturer(entity)
      return if params[:create_manufacturer].blank?

      manufacturer = entity.search_for_related_manufacturer

      if manufacturer.blank?
        manufacturer = Mkp::Manufacturer.create!(name: entity.name)

        if manufacturer.blank?
          flash[:error] = 'There was an error creating the manufacturer'
          render :new and return
        end
      end

      entity.profile.update_attribute(:manufacturer_id, manufacturer.id)
    end

    def create_merchant(entity)
      if params[:create_merchant].present?
        @shop = Shop.new
        @shop.title = entity.name
        @shop.network = @network
        @shop.visible = false
        if @shop.save!
          @shop.assign_owner(entity.id)
          @shop.assign_default_bna_admins
        else
          flash[:error] = 'There was an error creating the shop'
          render :new and return
        end
      end
    end

    def load_admins(entity)
      @admins = entity.admins.map{ |u| { user: u.admin, role: 'shop'} }
      @admins << entity.shop.shop_admins.map{ |u| { user: u.admin, role: 'shop'} } if entity.shop.present?
      @admins = @admins.flatten.uniq
      @available_roles = ['shop']
    end

    def set_managers(entity)
      shop_present = @shop.present?
      managers = params[:managers].select { |m| m[:user_id].present? } if params[:managers].present?
      if managers.present?
        managers.each do |manager|
          user = User.find(manager[:user_id])
          if manager[:role] == 'none'
            entity.admins.delete(user)
            @shop.admins.delete(user) if shop_present
          elsif manager[:role] == 'all'
            assign_as_admin(entity, user)
            @shop.assign_merchant(user) if shop_present
          elsif manager[:role] == 'community'
            assign_as_admin(entity, user)
            @shop.admins.delete(user) if shop_present
          elsif manager[:role] == 'shop'
            entity.admins.delete(user)
            @shop.assign_merchant(user) if shop_present
          end
        end
      end
    end

    def update_mentions(entity, login_was)
      return unless login_was
      Notification::Mentioned.where(user_id: entity.id).each do |notification|
        notification.event[:post_title].gsub!("@#{login_was}", "@#{entity.login}")
        notification.save!
        whatsup = notification.event[:post_type].constantize.find(notification.event[:post_id])
        whatsup.update_column(:body, whatsup.body.gsub!("@#{login_was}", "@#{entity.login}")) if whatsup.body
      end
    end

  end
end
