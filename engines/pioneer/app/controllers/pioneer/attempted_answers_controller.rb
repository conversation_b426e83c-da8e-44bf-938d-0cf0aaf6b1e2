module Pioneer
  class AttemptedAnswersController < Pioneer::ApplicationController
    before_filter :set_filter_values, only: [:index]
    before_filter :find_answers, only: [:destroy]
    before_filter :check_ns_role
    before_action :set_stores, only: [:new, :create, :index]

    layout 'bootstrap_layout'

    def index
      @types_answer_attepmts = AnswerAttempt::AttemptedAnswer.types
      @attempted_answers = AnswerAttempt::AttemptedAnswer.group(:store_id, :doc_number, :type).count
      @attempted_answers = @store_id_query == 0 || @store_id_query.nil? ? @attempted_answers : @attempted_answers.select{|key, val| key[0] == @store_id_query }
      @attempted_answers = @type_query == "0" || @type_query.nil? ? @attempted_answers : @attempted_answers.select{|key, val| key[2] == @type_query}
      @attempted_answers = @attempted_answers.select{|key, val| key[1] == @search_query } if @search_query.present?
      @attempted_answers = @attempted_answers.sort_by{|key, val| val}.reverse.paginate(page: params[:page])

      # Preload stores to avoid N+1
      store_ids = @attempted_answers.map { |key, _| key[0] }.uniq
      @stores_cache = Mkp::Store.where(id: store_ids).index_by(&:id)

      # Preload blacklist data to avoid N+1
      doc_store_pairs = @attempted_answers.map { |key, _| [key[1], key[0]] }
      @blacklist_cache = Pioneer::Blacklist.where(
        doc_number: doc_store_pairs.map(&:first),
        store_id: doc_store_pairs.map(&:second)
      ).pluck(:doc_number, :store_id).to_set

      # Preload recent attempts for blocking service to avoid N+1
      @recent_attempts_cache = AnswerAttempt::AttemptedAnswer.where(
        doc_number: doc_store_pairs.map(&:first),
        store_id: doc_store_pairs.map(&:second),
        created_at: 72.hours.ago..Time.current
      ).group_by { |attempt| [attempt.doc_number, attempt.store_id] }
    end

    def new
      @attempted_answer = AnswerAttempt::AttemptedAnswer.new()
    end

    def create
      3.times do |_i|
        @attempted_answer = AnswerAttempt::AttemptedAnswer.create(doc_number: attempted_answer_params["doc_number"],
                                                                  store_id: attempted_answer_params["store_id"],
                                                                  question_id: 99,
                                                                  question: "INGRESADO MANUALMENTE",
                                                                  pioneer_admin_id: @current_user.id,
                                                                  type: "AnswerAttempt::Equifax")
        if !@attempted_answer.valid?
          flash[:error] = @attempted_answer.errors.full_messages
          render :new
          return
        end
      end
      @attempted_answer.save!
      redirect_to attempted_answers_path, notice: 'Se ha agregado un nuevo Documento a la lista.'
    end

    def destroy
      @attempted_answers_to_remove.destroy_all
      redirect_to attempted_answers_url
    end

    def import
      valid_store_ids = ['41', '47']
      full_path = params[:dni][:file].tempfile
      CSV.foreach(full_path, headers: true) do |row|
        AttemptedAnswerDniWorker.perform_async(row.to_h, @current_user.id, valid_store_ids)
      end
      redirect_to attempted_answers_path, notice: '¡Excelente! En breve, nuevos documentos serán agregados a la lista.'
    end

    private

    def find_answers
      @attempted_answers_to_remove ||= AnswerAttempt::AttemptedAnswer.where(doc_number: params[:id])
    end

    def set_filter_values
      @search_query = params[:query] if params[:query].present?
      @type_query = params[:type] if params[:type].present?
      @store_id_query = params[:store_id].to_i if params[:store_id].present?
    end

    def attempted_answer_params
      params.require(:answer_attempt_attempted_answer).permit(:store_id, :doc_number)
    end

    def set_stores
      @stores = current_user.role.is_a?("administrator") ? Mkp::Store.all.order(name: :asc) : [current_user.role.store]
    end

    def check_ns_role
      redirect_to home_url, alert: 'Have not permissions' unless current_user.nacion_renaper_admin? or current_user.has_role?(:administrator)
    end
  end
end
