require 'rails_helper'

RSpec.describe Mkp::Cart, type: :model do
  subject(:cart) { build :cart }

  describe 'associations' do
    it { is_expected.to have_many(:delivery_options) }
  end

  # rubocop:disable RSpec/MultipleMemoizedHelpers
  describe '#build_delivery_options' do
    # rubocop:disable Naming/VariableNumber
    let!(:store_41) { create(:store, id: 41) }
    let!(:store_21) { create(:store, id: 21) }
    # rubocop:enable Naming/VariableNumber
    let!(:shop) { create(:shop, delivery_by_matrix: false) }
    let!(:warehouse) { create(:warehouse, shop: shop, zip: '1000') }

    let(:prepared_cart) do
      customer = create(:customer)
      category = create(:category)
      manufacturer = create(:manufacturer)
      product = create(:product, shop: shop, category: category, manufacturer: manufacturer)
      variant = create(:variant, product: product, sku: 'SKU123')
      address = create(:address, zip: '2000')
      cart = create(:cart, address: address, customer: customer)
      create(:cart_item, cart: cart, variant: variant)

      OpenStruct.new(
        cart: cart,
        customer: customer,
        category: category,
        manufacturer: manufacturer,
        product: product,
        variant: variant,
        address: address
      )
    end

    let(:suborder) { instance_double('Suborder', shop: shop, get_suborder_weight: 2, get_suborder_height: 10, get_suborder_width: 10, get_suborder_length: 10, total: 240.0, subtotal: 200.0) }

    let(:suborder_item) do
      instance_double('Item',
                      weight: 1,
                      length: 10,
                      width: 5,
                      height: 3,
                      quantity: 1,
                      product: prepared_cart.product,
                      variant: prepared_cart.variant,
                      suborder: suborder)
    end

    let(:suborder_items) { [suborder_item] }

    let(:krabpack_delivery_options_service_stub) do
      instance_double(
        Gateways::Shipments::Krabpack::SuborderDeliveryOptionsService,
        valid: true,
        response: [
          {
            carrier: 'Correo Argentino',
            price: 120.0,
            service: 'Standard'
          }.with_indifferent_access
        ]
      )
    end

    let(:coati_stub_response) do
      {
        quotes: [
          {
            'carrier' => 'OCA',
            'price' => 524.15,
            'deliveryTerm' => 2,
            'service' => 'DOOR_TO_DOOR_STANDARD'
          }
        ],
        errors: {}
      }.to_json
    end

    before do
      shop.stores << store_41
      shop.stores << store_21

      allow(suborder).to receive(:items).and_return(suborder_items)

      allow(Mkp::PurchaseProcessor).to receive(:send).with(:suborder_generator, prepared_cart.cart).and_return([suborder])
      allow_any_instance_of(Mkp::Cart).to receive(:virtual_delivery).with(suborder).and_return(false)

      allow(Gateways::Shipments::Coati::Manager).to receive(:quote_shipping)
        .and_return([
                      {
                        'quote_item_id' => 'servicio_externo-0',
                        'price' => 120.0,
                        'carrier' => 'OCA',
                        'delivery_term' => '3',
                        'service_level_real' => 'Standard',
                        'pickup_address' => nil,
                        'pickup' => false,
                        'pickup_code' => nil,
                        'service' => 'Standard'
                      }
                    ])

      # Stub Krabpack
      allow(Gateways::Shipments::Krabpack::SuborderDeliveryOptionsService).to receive(:new)
        .and_return(krabpack_delivery_options_service_stub)

      allow(krabpack_delivery_options_service_stub).to receive(:perform).and_return(true)

      allow(prepared_cart.cart).to receive(:shops).and_return([shop])

      stub_request(:post, 'https://api-stg.coatiapp.com/auth/quotes')
        .to_return(status: 200, body: coati_stub_response, headers: { 'Content-Type' => 'application/json' })
    end

    it 'persists delivery options using the external service response' do
      prepared_cart.cart.build_delivery_options(prepared_cart.cart)
      options = prepared_cart.cart.delivery_options

      expect(options.first.charge).to eq(120.0)
      expect(options.first.title).to eq('Correo Argentino - Standard')
    end

    it 'calls the external shipping service with correct parameters' do
      # rubocop:disable RSpec/MessageSpies
      expect(Gateways::Shipments::Coati::Manager).to receive(:quote_shipping).with(
        store: store_41,
        cuit: shop.cuit,
        originZipCode: warehouse.zip,
        destinationZipCode: prepared_cart.address.zip,
        products: kind_of(Array)
      ).and_call_original
      # rubocop:enable RSpec/MessageSpies

      prepared_cart.cart.build_delivery_options(prepared_cart.cart)

      expect(WebMock).to have_requested(:post, 'https://api-stg.coatiapp.com/auth/quotes')
    end

    it 'doesnt call the external shipping service if coati is disabled' do
      store_41.update(coati_enabled: false)

      # rubocop:disable RSpec/MessageSpies
      expect(Gateways::Shipments::Coati::Manager).to receive(:quote_shipping).with(
        store: store_41,
        cuit: shop.cuit,
        originZipCode: warehouse.zip,
        destinationZipCode: prepared_cart.address.zip,
        products: kind_of(Array)
      ).and_call_original
      # rubocop:enable RSpec/MessageSpies

      prepared_cart.cart.build_delivery_options(prepared_cart.cart)

      expect(WebMock).to have_requested(:post, 'https://api-stg.coatiapp.com/auth/quotes')
    end
  end
  # rubocop:enable RSpec/MultipleMemoizedHelpers
end
