FactoryBot.define do
  factory :address, class: 'Mkp::Address' do
    association :addressable, factory: :customer

    first_name { '<PERSON><PERSON><PERSON>' }
    last_name { 'Pica<PERSON>' }
    telephone { '456789' }
    address { 'larrea 1345' }
    address_2 { nil }
    city { 'CABA' }
    state { 'Capital Federal' }
    country { 'AR' }
    zip { '1117' }
    doc_number { nil }
    pickup { nil }
    open_hours { nil }
    retardment { nil }
    priority { nil }
    deleted_at { nil }
    type { 'Mkp::Address' }
    doc_type { nil }
    street_number { nil }
    customer_id { nil }
    smartcarrier_id { nil }
    floor { nil }
    dpto { nil }
    tower { nil }
    body { nil }
    lateral_street_1 { nil }
    lateral_street_2 { nil }
    county { nil }
    country_club { nil }
    internal_country_address { nil }
    closing_hours { nil }
    billing_address { nil }
  end
end
