FactoryBot.define do
  factory :order, class: 'Mkp::Order' do
    association :customer, factory: [:customer, :with_addresses]
    association :store

    ip { '127.0.0.1' }
    network { 'local' }
    purchase_id { SecureRandom.hex(10) }
    title { 'Pedido de prueba' }
    gross_total { 100.50 }

    created_at { Time.current }
    updated_at { Time.current }

    coupon_discount { 0.0 }
    auto_invoiced { false }
    bonified_amount { 0.0 }
    jubilo_liquided { false }
    reservation_status { 0 }
    points { 0 }
    points_money { 0.0 }
  end
end
