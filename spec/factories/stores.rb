FactoryBot.define do
  factory :store, class: 'Mkp::Store' do
    name { '<PERSON>ni<PERSON>' }
    token { '12312312312' }
    title { 'Aveni<PERSON>' }
    active { true }
    logo { nil }
    gateway { nil }
    email { Faker::Internet.free_email }
    inherit_free_shipping { nil }
    hostname { 'https://avenida.com' }
    percentage_fee { 0 }
    image_dues_file_name { nil }
    image_dues_content_type { nil }
    image_dues_file_size { nil }
    image_dues_updated_at { nil }
    facebook { 'https://avenida.com' }
    instagram { 'https://avenida.com' }
    twitter { 'https://avenida.com' }
    telephone { '12323232323' }
    popup_image { nil }
    invoiceable { false }
    preferred_sorting { nil }
    abandoned_cart_email { Faker::Internet.safe_email }
    production { true }
    equivalent_points { nil }
    visa_puntos_equivalence { nil }
    visa_puntos_api_key { nil }
    visa_puntos_sube_equivalence { nil }
    visa_puntos_recargas_equivalence { nil }
    product_approval { nil }
    allow_orders_cancellation { nil }
    use_bines { nil }
    allow_meli_integration { nil }
    insurance_source { nil }
    limit_amount { 100000 }
    coati_enabled { true }
  end
end
