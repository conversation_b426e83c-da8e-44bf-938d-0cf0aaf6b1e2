FactoryBot.define do
  factory :cart, class: 'Mkp::Cart' do
    customer_id { 10691 }
    customer_type { 'Mkp::Customer' }
    purchase_id { 'TpdowzFGyJl53T_uFwrD1w' }
    status { 'checkout' }
    ip { nil }
    network { 'AR' }
    type { nil }
    coupon_id { nil }
    data { nil }
    store_id { 41 }
    address_id { 179287 }
    delivery_option_id { nil }
    is_pickup { 0 }
    newsletter { true }
    left_cart_email { false }
  end

  # TODO configurar para test de integración
  # factory :cart_with_all_dependencies, parent: :cart do
  #   association :customer
  #   association :address
  #
  #   transient do
  #     shop { create(:shop) }
  #   end
  #
  #   after(:create) do |cart, evaluator|
  #     category = create(:category)
  #     manufacturer = create(:manufacturer)
  #     package = create(:package)
  #     product = create(:product, shop: evaluator.shop, category: category, manufacturer: manufacturer, packages: [package])
  #     variant = create(:variant, product: product)
  #     create(:cart_item, cart: cart, variant: variant)
  #   end
  # end
end
