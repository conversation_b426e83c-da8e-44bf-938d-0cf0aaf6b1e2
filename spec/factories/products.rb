FactoryBot.define do
  factory :product, class: 'Mkp::Product' do
    association :currency
    association :manufacturer
    association :shop
    association :category

    regular_price { 100 }
    title { Faker::Commerce.product_name }
    description { Faker::Commerce.product_name }
    slug { Faker::Internet.unique.slug }

    after(:build) do |product|
      product.packages << build(:package, product: product)
    end
  end
end
