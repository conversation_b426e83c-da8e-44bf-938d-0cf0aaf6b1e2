FactoryBot.define do
  factory :shipment, class: 'Mkp::Shipment' do
    association :suborder

    status { 'unfulfilled' }

    trait :gateway_coati do
      transient do
        address { build(:address) }
        address2 { build(:address) }
      end

      gateway { 'Coati' }

      origin_address {
        OpenStruct.new(
          street: address.address,
          city: address.city,
          zip: address.zip,
          province: address.state
        )
      }

      destination_address {
        OpenStruct.new(
          street: address2.address,
          city: address2.city,
          zip: address2.zip,
          province: address2.state
        )
      }
    end

    after(:build) do |shipment|
      item = build(:order_item, suborder: shipment.suborder)
      shipment.items << item
    end
  end
end
