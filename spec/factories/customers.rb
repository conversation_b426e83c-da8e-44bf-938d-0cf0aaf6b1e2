FactoryBot.define do
  factory :customer, class: 'Mkp::Customer' do
    email { 'cremash<PERSON><PERSON><PERSON><PERSON>@hotmail.com' }
    first_name { '<PERSON> Beat<PERSON> ' }
    last_name { '<PERSON><PERSON>' }
    image { nil }
    doc_number { '11769900' }
    provider { nil }
    uuid { nil }
    gender { 1 }
    birthday_at { '1956-01-19 03:00:00' }
    telephone { '**********' }
    user_identify { nil }
    doc_type { 0 }
    temporary_email { false }
    macro_category { nil }
    validator { 'Equifax' }
    store
    password { 'test123' }
    password_confirmation { 'test123' }

    trait :with_addresses do
      after(:create) do |customer|
        create_list(:address, 2, addressable: customer)
      end
    end
  end
end
