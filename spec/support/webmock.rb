require 'webmock/rspec'

WebMock.disable_net_connect!(allow_localhost: true)

RSpec.configure do |config|
  config.before do
    stub_request(:post, 'https://api.test.krabpack.com/shippingquote')
      .with(
        body: hash_including(
          'contact' => hash_including('firstname' => kind_of(String)),
          'created_by' => kind_of(String),
          'to' => hash_including('postal_code' => kind_of(String))
        ),
        headers: {
          # 'Apikey' => 'fake-api-key',
          'Apikey' => 'uOpLhuSRWslMCOJRdGBUZOXNuBfPRRiP',
          'Content-Type' => 'application/json'
        }
      )
      .to_return(status: 200, body: '{}', headers: { 'Content-Type' => 'application/json' })

    stub_request(:post, 'https://chat.avenida.com.ar/hooks/').
      with(
        body: "{\"text\":\"pop-os - 🚨 Envío con costo predeterminado\\nStore: Avenida\\nShop: Avenida (id: 2)\\nMotivo: Peso no contemplado en matriz (2)\",\"attachment\":[{}]}",
        headers: {
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'Content-Type'=>'application/json',
          'User-Agent'=>'Faraday v0.9.2'
        }).
      to_return(status: 200, body: "", headers: {})
  end
end
