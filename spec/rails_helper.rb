# This file is copied to spec/ when you run 'rails generate rspec:install'
# require database cleaner at the top level
require 'database_cleaner'
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'

require File.expand_path('../config/environment', __dir__)

# Safety check: Prevent running tests on the wrong database (e.g., production or staging).
if Rails.env.test?
  begin
    db_name = ActiveRecord::Base.connection_config[:database]
  rescue ActiveRecord::NoDatabaseError, ActiveRecord::ConnectionNotEstablished
    require 'yaml'
    db_config = YAML.load_file(File.expand_path('../../config/database.yml', __FILE__))
    db_name = db_config['test']['database']
  end

  accepted_db = 'avenida_platform_test'

  unless db_name == accepted_db
    abort("❌ ABORTED: Test suite is connected to '#{db_name}', but only '#{accepted_db}' is allowed for testing.")
  end
end

# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?
require 'rspec/rails'
require 'shoulda-matchers'
require 'factory_bot_rails'
require 'faker'

Dir[Rails.root.join("spec/support/**/*.rb")].sort.each { |f| require f }

# DISABLE foreign key checks to allow schema loading (especially useful for MySQL)
begin
  ActiveRecord::Base.connection.execute("SET FOREIGN_KEY_CHECKS = 0;")
  ActiveRecord::Migration.maintain_test_schema!
ensure
  ActiveRecord::Base.connection.execute("SET FOREIGN_KEY_CHECKS = 1;")
end

SimpleCov.start 'rails' do
  add_filter '/bin/'
  add_filter '/db/'
  add_filter '/spec/' # for rspec
  add_filter '/test/' # for minitest
end


RSpec.configure do |config|
  config.use_transactional_fixtures = true
  config.infer_spec_type_from_file_location!
  config.filter_rails_from_backtrace!
  config.include FactoryBot::Syntax::Methods
  config.include(Shoulda::Matchers::ActiveModel, type: :model)
  config.include(Shoulda::Matchers::ActiveRecord, type: :model)

  config.before(:suite) do
    # Stub Solr (Sunspot) to avoid hitting the real search index during tests
    Sunspot.session = Sunspot::Rails::StubSessionProxy.new(Sunspot.session)

    # Temporarily disable foreign key checks for full truncation
    ActiveRecord::Base.connection.execute("SET FOREIGN_KEY_CHECKS = 0;")
    DatabaseCleaner.clean_with(:truncation)
    ActiveRecord::Base.connection.execute("SET FOREIGN_KEY_CHECKS = 1;")

    # Set default cleaning strategy to transaction
    DatabaseCleaner.strategy = :transaction

    Faker::UniqueGenerator.clear
  end

  config.around(:each) do |example|
    DatabaseCleaner.cleaning do
      example.run
    end
  end

  config.include FactoryBot::Syntax::Methods
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
    with.library :active_record
    with.library :active_model
  end
end
