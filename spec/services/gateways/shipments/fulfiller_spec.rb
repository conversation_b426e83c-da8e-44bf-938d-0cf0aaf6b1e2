require 'rails_helper'

def setup_shipments_with_store_41
  store_41 = create(:store, id: 41)
  shop = create(:shop)
  shop.stores << store_41

  product = create(:product, shop: shop)
  variant = create(:variant, product: product)
  order = create(:order)
  suborder = create(:suborder, order: order, shop: shop)

  order_item = create(:order_item, product: product, variant: variant, suborder: suborder)

  shipment1 = create(:shipment, :gateway_coati, suborder: suborder).tap do |s|
    s.items << order_item
    s.save!
  end

  shipment2 = create(:shipment, :gateway_coati, suborder: suborder).tap do |s|
    s.items << order_item
    s.save!
  end

  shipment3 = create(:shipment, :gateway_coati)

  [shipment1, shipment2, shipment3]
end

RSpec.describe Gateways::Shipments::Fulfiller do
  describe '#shipments_scope' do
    it 'returns only shipments in unfulfilled status' do
      _shipment1, _shipment2, _shipment3 = setup_shipments_with_store_41
      fulfiller = described_class.new

      scope = fulfiller.send(:shipments_scope)
      expect(scope.count).to eq(2)
    end
  end

  describe '#process_all for Coati shipments' do
    let(:fulfiller) { described_class.new }

    let(:coati_response) do
      instance_double('DeliveryResponse',
                      tracking_number: 'TRK123456789',
                      coati_id: 'mocked_coati_id_001')
    end

    before do
      allow(Gateways::Shipments::Coati::Manager)
        .to receive(:create_shipping)
        .and_return(coati_response)

      allow(Gateways::Shipments::Coati::Manager)
        .to receive(:create_return_shipping)
        .and_return(coati_response)

      allow(Mkp::ShipmentLabel).to receive(:create).and_return(Mkp::ShipmentLabel.new)
    end

    it 'process all unfulfilled shipments' do
      shipment1, shipment2, shipment3 = setup_shipments_with_store_41

      fulfiller.process_all

      aggregate_failures 'shipment statuses' do
        expect(Mkp::ShipmentLabel).to have_received(:create).at_least(:once) do |args|
          expect(args[:gateway_data]).to be_a(Hash)
        end

        expect(shipment1.reload.status).to eq('in_process')
        expect(shipment2.reload.status).to eq('in_process')
        expect(shipment3.reload.status).to eq('unfulfilled')
      end
    end

    it 'does not change state if Coati fails' do
      allow(Gateways::Shipments::Coati::Manager).to receive(:create_shipping).and_return(false)

      shipment, = setup_shipments_with_store_41
      fulfiller.process_all

      aggregate_failures do
        expect(shipment.reload.status).to eq('unfulfilled')
        expect(Mkp::ShipmentLabel).not_to have_received(:create)
      end
    end

    it 'creates the label with the correct data' do
      shipment, = setup_shipments_with_store_41

      fulfiller.process_all

      hash = hash_including(
        shipment_id: shipment.id,
        tracking_number: 'TRK123456789',
        gateway_data: hash_including(coati_id: 'mocked_coati_id_001')
      )

      expect(Mkp::ShipmentLabel).to have_received(:create).with(hash)
    end

    it 'uses create_return_shipping for refunds' do
      shipment, = setup_shipments_with_store_41
      shipment.update!(shipment_kind: 'refund')

      fulfiller.process_all

      expect(Gateways::Shipments::Coati::Manager).to have_received(:create_return_shipping)
    end

    it 'logs errors when Coati fails' do
      setup_shipments_with_store_41
      allow(Gateways::Shipments::Coati::Manager).to receive(:create_shipping).and_raise('Boom')

      messages = []
      allow(Rails.logger).to receive(:info) { |msg| messages << msg }

      fulfiller.process_all

      expect(messages.any? { |msg| msg =~ /Coati Error en envío \d+: Boom/ }).to be(true)
    end

    it 'does not process shipments if processing_started_at is not null' do
      shipment, = setup_shipments_with_store_41
      shipment.update(processing_started_at: DateTime.now)

      fulfiller.process_all

      expect(shipment.reload.status).to eq('unfulfilled')
    end

    it 'process shipments if processing_started_at is null' do
      shipment, = setup_shipments_with_store_41
      shipment.update(processing_started_at: nil)

      fulfiller.process_all

      expect(shipment.reload.status).to eq('in_process')
    end

    it 'process shipments if processing_started_at is greater than 60 minutes' do
      shipment, = setup_shipments_with_store_41
      shipment.update(processing_started_at: DateTime.now - 2)

      fulfiller.process_all

      expect(shipment.reload.status).to eq('in_process')
    end
  end
end
