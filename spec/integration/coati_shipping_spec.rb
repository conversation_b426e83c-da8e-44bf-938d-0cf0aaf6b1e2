require 'rails_helper'

RSpec.describe 'Coati Shipping API Integration', type: :integration do
  # TODO hacer test de integración


  # let!(:store) { create(:store, id: 41) }
  # let!(:shop) { create(:shop, delivery_by_matrix: false) }
  # let!(:warehouse) { create(:warehouse, shop: shop, zip: '1000') }
  #
  # let!(:customer) { create(:customer) }
  # let!(:category) { create(:category) }
  # let!(:manufacturer) { create(:manufacturer) }
  # let!(:package) { create(:package) }
  # let!(:product) { create(:product, shop: shop, category: category, manufacturer: manufacturer, packages: [package]) }
  # let!(:variant) { create(:variant, product: product, sku: 'SKU123') }
  # let!(:address) { create(:address, zip: '2000') }
  #
  # let!(:cart) do
  #   c = create(:cart, address: address, customer: customer, store: store)
  #   create(:cart_item, cart: c, variant: variant)
  #   c
  # end
  #
  # let(:coati_stub_response) do
  #   {
  #     quotes: [
  #       {
  #         'carrier' => 'OCA',
  #         'price' => 524.15,
  #         'deliveryTerm' => 2,
  #         'service' => 'DOOR_TO_DOOR_STANDARD'
  #       }
  #     ],
  #     errors: {}
  #   }.to_json
  # end
  #
  # before do
  #   shop.stores << store
  #   shop.warehouses << warehouse
  #
  #   # Stub HTTP request to Coati API
  #   stub_request(:post, 'https://api-stg.coatiapp.com/auth/quotes')
  #     .to_return(status: 200, body: coati_stub_response, headers: { 'Content-Type' => 'application/json' })
  # end
  #
  # it 'builds and persists delivery options from Coati API response' do
  #   # Call the method under test
  #   cart.build_delivery_options(cart)
  #
  #   # Check delivery options created in DB
  #   delivery_options = cart.delivery_options.reload
  #   expect(delivery_options.count).to be > 0
  #
  #   coati_option = delivery_options.find_by(title: /OCA/)
  #   expect(coati_option).not_to be_nil
  #   expect(coati_option.charge).to eq(524.15)
  # end
end
