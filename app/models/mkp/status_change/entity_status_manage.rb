# frozen_string_literal: true
#
module Mkp
  module StatusChange
    class EntityStatusManage
      class << self
        def status_change(entity, status, skip_post_actions: false)
          # si no esta en el nuevo estado, procedo con el cambio
          unless entity.send("#{status}?")
            send("#{entity.class.name.split('::').last.downcase}_status_change!",
                 entity,
                 status,
                 skip_post_actions: skip_post_actions)
            # valido que haya cambiado de estado para registrar el status_change
            entity.reload
            if entity.send("#{status}?")
              Mkp::StatusChange::EntityStatusChange.create(entity: entity, status: status)
            end
          end
        end

        private

        def valid_status_change(entity, status)
          entity.send("#{status}?") # TODO: REVISAR PORQUE NO ESTA FUNCIONANDO! -> && entity.changed?
        end

        def orderitem_status_change!(entity, status, skip_post_actions: false)
          Mkp::StatusChange::OrderItemStatusManage.status_change(entity,
                                                                 status,
                                                                 skip_post_actions: skip_post_actions)
        end

        def shipment_status_change!(entity, status, skip_post_actions: false)
          Mkp::StatusChange::ShipmentStatusManage.status_change(entity,
                                                                status,
                                                                skip_post_actions: skip_post_actions)
        end
      end
    end
  end
end
