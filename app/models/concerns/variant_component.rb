module VariantComponent
  extend ActiveSupport::Concern

  def featured_variants_ids
    fixed_variants_ids.take(self.class::N_OF_ITEMS)
  end

  def fixed_variants_ids
    return @fixed_variants_ids if defined?(@fixed_variants_ids)
  
    fixed_variants = retrieve_fixed_variants
  
    if @store.product_approval
      search = search_variants
      valid_variants_ids = search.hits.map { |h| h.primary_key.to_i }
      fixed_variants = fixed_variants.where(id: valid_variants_ids)
    end
  
    # Optimiza consulta y evita duplicados
    @fixed_variants_ids = fixed_variants
      .select('mkp_variants.id')
      .group(:product_id)
      .order('MIN(mkp_variants.id)')
      .limit(self.class::N_OF_ITEMS)
      .pluck(:id)
  end

  def valid_variants_ids
    return @search_variants_ids unless @search_variants_ids.nil?

    categories = Mkp::Product.joins(:category).where(id: products_ids).merge(Mkp::Category.active).distinct(:category_id).pluck(:category_id)
    total = self.class::N_OF_ITEMS - fixed_variants_ids.length
    @search_variants_ids = search_variants(fixed_variants_ids, {categories_ids: categories}).hits.map{ |h| h.primary_key.to_i }
    return @search_variants_ids
  end

  def products_ids
    Mkp::Variant.where(id: fixed_variants_ids).pluck(:product_id)
  end

  def retrieve_fixed_variants
    product_skus = setup[:items].map { |hash| hash['variant_product_sku'] }.compact.uniq.reject(&:blank?)
    if !product_skus.empty?
      query = Mkp::Variant.active.with_stock.merge(build_product_sku_condition(product_skus))
    else
      gp_skus = setup[:items].map { |hash| hash['variant_gp_sku'] }.compact.uniq.reject(&:blank?)
      if !gp_skus.empty?
        query = Mkp::Variant.active.with_stock.merge(build_like_condition(gp_skus))
      else
        ids = setup[:items].map { |hash| hash['variant_id'] }.compact.uniq.reject(&:blank?)
        query = Mkp::Variant.active.with_stock.where(id: ids)
      end
    end
    fixed_variants = query.where(shop_id: store.active_shops.pluck(:shop_id))
  end

  def build_like_condition(gp_skus)
    query = Mkp::Variant.none
    gp_skus.each do |gp_sku|
      query = query.or(Mkp::Variant.gp_sku_like(gp_sku))
    end
    query
  end

  def build_product_sku_condition(gp_skus)
    query = Mkp::Variant.none
    gp_skus.each do |gp_sku|
      product_id, *sku = gp_sku.split('-')
      query = query.or(Mkp::Variant.product_sku(product_id, sku.join('-')))
    end
    query
  end

end
