class Api::AngularApp::V1::ProductsController < Api::AngularApp::V1::ApplicationController
  before_action :set_store, only: :index
  before_action :set_shops, only: :index

  helper_method :get_max_installments_for_product, :get_ean_codes_for_product

  def show
    @product = Mkp::Product.find_by_slug(params[:id]) || Mkp::Product.find(params[:id])
    raise ActiveRecord::RecordNotFound unless @current_store.categories.include?(@product.category)
    raise ActiveRecord::RecordNotFound unless @current_store.active_shops.pluck(:shop_id).include?(@product.shop.id)
    raise ActiveRecord::RecordNotFound unless @product.is_approved_for_store?(@current_store)
  end

  def calculate_shipping
    product = Mkp::Product.find(params[:product_id])
    if product.present?
      if product.shop.delivery_by_matrix
        response = get_matrix_delivery_options(product)
      else
        service = Gateways::Shipments::Krabpack::GetDeliveryOptionsService.new(zip_code: params[:postal_code], product: product)
        service.perform

        response = service.valid ? service.response.first : get_matrix_delivery_options(product)
      end
    else
      response = { message: 'El producto no existe' }
    end
    render json: response
  end

  def stock_by_shopping
    stocks = params[:coupon_ids].map do |value|
      Irsa::Stock.get(value)
    end
    stocks = stocks.flatten.group_by{ |s| s["shopping"] }.map{ |s| { "shopping" => s[0], "stock" => s[1].sum{|u| u["stock"]} } if s[1].any?{ |m| m["stock"] > 0 } }.compact
    render json: stocks
    rescue
      render json: { error: "No tenemos el stock disponible", status: 401 }
  end

  def index
    return if params[:store_id].blank?

    @hostname = @store.hostname
    batch_size = 10000
    offset_value = 0

    @products = []

    loop do
      products_batch = Mkp::Product.includes(:packages, :category, :pictures, :shop, variants: :product)
                                   .where(shop_id: @shop_ids)
                                   .active
                                   .with_stock
                                   .limit(batch_size)
                                   .offset(offset_value)

      break if products_batch.empty?

      @products.concat(products_batch)

      offset_value += batch_size
    end
  end

  private

  def get_matrix_delivery_options(product)
    @current_store.get_delivery_options(params[:postal_code], [product.final_weight], [product.shop_id]).first
  end

  def per_page
    params[:format] == 'xml' ? 1_000_00 : 30
  end

  def set_store
    @store = Mkp::Store.find(params[:store_id])
  end

  def set_shops
    @shop_ids = @store.shops.visible.pluck(:shop_id)
  end

  def get_max_installments_for_product(product)
    bna_store = Mkp::Store.find_by(id: 41)
    return 0 unless bna_store
  
    product.category.max_installments(bna_store, product)
  rescue NoMethodError
    0
  end  

  def get_ean_codes_for_product(product)
    ean_codes = product.variants.where.not(ean_code: [nil, '']).pluck(:ean_code)
    ean_codes.present? ? ean_codes.join(',') : ''
  end
end
