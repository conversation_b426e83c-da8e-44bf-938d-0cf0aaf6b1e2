if @products.present?
  xml.instruct!
  xml.root do
    xml.catalogo do
      @products.each do |product|
        xml.producto do
          xml.id product.id
          xml.titulo product.title
          xml.descripcion product.description
          xml.precio product.regular_price.to_i
          xml.precio_descuento product.price.to_i
          xml.precio_a_mostrar product.price.to_i
          xml.imagen product.picture&.url
          xml.link "#{@hostname}/products/#{product.slug}"
          xml.categoria product.category.name
          xml.nombre_seller product.shop&.title
          xml.maximo_cuotas get_max_installments_for_product(product)
          xml.codigo_ean get_ean_codes_for_product(product)
        end
      end
    end
  end
end
