module IdentityValidation
  class AttemptBlockingService < ApplicationService
    attr_reader :doc_number, :store_id, :attempts, :blocking_result

    def initialize(args = {})
      @doc_number = args[:doc_number]
      @store_id = args[:store_id]
      @attempts = []
      @blocking_result = OpenStruct.new(blocked: false, message: nil, reason: nil)
    end

    def call
      # Check if permanently blocked (in blacklist)
      if permanently_blocked?
        permanent_block_result
        return self
      end

      # Get attempts from last 72 hours (3 days)
      @attempts = get_recent_attempts

      # Check permanent blocks first, then temporary blocks
      if should_block_permanently?
        add_to_permanent_blacklist
        permanent_block_result
        return self
      elsif should_block_temporarily?
        temporary_block_result
        return self
      end
      # Not blocked
      self
    end

    def blocked?
      @blocking_result.blocked == true
    end

    def message
      @blocking_result.message
    end

    def reason
      @blocking_result.reason
    end

    private

    def permanently_blocked?
      Pioneer::Blacklist.exists?(store_id: store_id, doc_number: doc_number)
    end

    def get_recent_attempts
      AnswerAttempt::AttemptedAnswer.where(
        store_id: store_id,
        doc_number: doc_number,
        created_at: 72.hours.ago..Time.current
      ).order(:created_at)
    end

    def should_block_temporarily?
      # Check if user has exceeded 4 attempts in the last 24 hours
      recent_attempts = attempts.select { |attempt| attempt.created_at >= 24.hours.ago }

      recent_attempts.count >= 4
    end

    def should_block_permanently?
      # Check if user has 12 or more attempts in the last 72 hours
      attempts.count >= 12
    end

    def attempts_by_day
      # Group attempts by day for the last 3 days
      days = {}
      3.times do |i|
        day_start = (i + 1).days.ago.beginning_of_day
        day_end = (i + 1).days.ago.end_of_day
        day_attempts = attempts.select { |attempt| attempt.created_at >= day_start && attempt.created_at <= day_end }
        days[i] = day_attempts.count if day_attempts.any?
      end
      days
    end

    def has_failed_all_daily_attempts?
      # Check if user has failed 4 attempts per day for 3 consecutive days
      daily_counts = attempts_by_day
      return false if daily_counts.keys.count < 3

      daily_counts.values.all? { |count| count >= 4 }
    end

    def temporary_block_result
      @blocking_result.blocked = true
      @blocking_result.message = 'Superaste el límite de intentos diarios. Podés volver a intentarlo en 24 horas.'
      @blocking_result.reason = :temporary_daily_limit
    end

    def permanent_block_result
      @blocking_result.blocked = true
      @blocking_result.message = 'Tu DNI ha sido bloqueado. Por favor, comunícate con nosotros al 0810-4444-500 (lunes a viernes de 8 a 20 h).'
      @blocking_result.reason = :permanent_block
    end

    def add_to_permanent_blacklist
      # Add to Pioneer::Blacklist for permanent blocking
      Pioneer::Blacklist.find_or_create_by(
        store_id: store_id,
        doc_number: doc_number,
        doc_type: 'DNI'
      )
    end

    # Method to reset attempts when manually removed from blacklist
    def self.reset_attempts_for_dni(doc_number, store_id)
      AnswerAttempt::AttemptedAnswer.where(
        store_id: store_id,
        doc_number: doc_number
      ).destroy_all
    end
  end
end
