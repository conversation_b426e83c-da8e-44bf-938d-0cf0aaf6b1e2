module Renaper
  class AttemptAnswer < ApplicationService
    attr_reader :doc_number, :gender, :purchase_id, :current_store, :cart, :real_remote_ip, :questions, :response

    include Api::AngularApp::V1::Concerns::RenaperVerifiable

    def initialize(args = {})
      @doc_number = args[:doc_number]
      @gender = args[:gender]
      @purchase_id = args[:purchase_id]
      @current_store = args[:current_store]
      @questions = args[:questions]
      @cart = args[:cart]
      @real_remote_ip = args[:real_remote_ip]

      @response = @questions.dup
    end

    def call
      status = :ok
      token = nil

      # Check blocking status using new service
      blocking_result = IdentityValidation::AttemptBlockingService.call(
        doc_number: doc_number,
        store_id: current_store.id
      )

      if blocking_result.blocked?
        cart.destroy if cart.present? # Al 12do intento se le debe vaciar el cart (removerlo)
        return OpenStruct.new(
          success: false,
          message: blocking_result.message,
          status: :unauthorized
        )
      end

      #Se le da prioridad por purchase_id
      pending_answer = pending_data_entry_question({:doc_number => doc_number, :gender => gender_customer_to_renaper(gender), :purchase_id => purchase_id, :question_id => questions.first["id"]}) || pending_data_entry_question({:doc_number => doc_number, :gender => gender_customer_to_renaper(gender), :question_id => questions.first["id"]})
      return OpenStruct.new(success: false, message: 'No tiene preguntas pendientes', status: :not_found) unless pending_answer.present?

      if pending_answer.is_correct?(questions.first['response'])
        response.first["success"] = true
        attempted_answer.each(&:destroy)
        pending_answer.purchase_id = purchase_id
        pending_answer.correct!
        token = pending_answer.token
      else
        status = 422
        response.first["success"] = false
        AnswerAttempt::Renaper.create({answer: questions.first['response'], doc_number: doc_number, gender: gender_customer_to_renaper(gender), store_id: current_store.id, correct: false, ip: real_remote_ip, question_id: pending_answer.question_id, question: pending_answer.question, question_type: pending_answer.question_type})
      end
      response.first["answers"] = questions.first["answers"]
      OpenStruct.new(type: :renaper, questions: response, token: token, status: status)
    end
  end
end
