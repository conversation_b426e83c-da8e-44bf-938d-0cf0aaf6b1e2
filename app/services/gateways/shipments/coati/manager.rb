require 'uri'
require 'net/http'
require 'openssl'

module Gateways
  module Shipments
    module Coati
      class Manager

        HTTP_METHODS = {
          get: Net::HTTP::Get,
          post: Net::HTTP::Post,
          put: Net::HTTP::Put,
          patch: Net::HTTP::Patch,
          delete: Net::HTTP::Delete
        }.freeze

        HTTP_METHODS_WITH_BODY = %i[post patch].freeze

        def self.quote_shipping(
          store: store,
          cuit: String | nil,
          originZipCode: Integer | nil,
          destinationZipCode: Integer | nil,
          products:
        )
          begin
            return [] unless coati_enabled?(store)

            raise 'No hay cuit para cotizar el envio' unless valid_cuit?(cuit)
            raise 'No hay originZipCode para cotizar el envio' if originZipCode.nil?
            if destinationZipCode.nil?
              raise 'No hay destinationZipCode configurado para cotizar el envio'
            end
            if !products.is_a?(Array) || products.size == 0
              raise 'No hay productos configurados para cotizar el envio'
            end

            body = {
              "originZipCode": originZipCode,
              "destinationZipCode": destinationZipCode,
              "packages": products.map(&:to_sym),
              "merchantCuit": cuit
            }.to_json

            Rails.logger.info({ service: 'COATI', event: 'request_body', body: body })
            response = request(:post, 'auth/quotes', body)

            unless response&.code.to_i == 200
              Rails.logger.error({
                                   service: 'COATI',
                                   event: 'bad_response',
                                   code: response&.code,
                                   body: response&.body
                                 })
            end

            if !response.nil? && response.code.to_i == 200
              result = JSON.parse(response.body)
              Rails.logger.info({ service: 'COATI', event: 'response_body', response: response.body })
              if result.key?('quotes') && !result['quotes'].empty?
                return result['quotes'].map.with_index do |d, inx|
                  {
                    'quote_item_id' => "coati-#{inx}",
                    'price' => d['price'],
                    'carrier' => d['carrier'],
                    'delivery_term' => d['deliveryTerm'],
                    'service_level_real' => d['service'],
                    'pickup_address' => d['agencyAddress'] || nil,
                    'pickup' => d['service'].upcase == 'PICKUP',
                    'pickup_code' => d['agencyCode'] || nil,
                    'service' => case d['service'].upcase
                                 when 'DOOR_TO_DOOR_STANDARD'
                                   'Standard'
                                 when 'DOOR_TO_DOOR_PRIORITY'
                                   'Prioritario'
                                 when 'PICKUP'
                                   'Retiro en sucursal'
                                 else
                                   d['service']
                                 end
                  }
                end
              elsif result.key?('error')
                raise "#{result['error']} - #{result['extraInfo']}"
              end
            end
          rescue StandardError => e
            Rails.logger.info("Coati Error: #{e.message}")
          end
          []
        end

        def self.create_shipping(
          store: store,
          carrier: String,
          cuit: String | nil,
          external_id: String,
          origin_address: ,
          destination_address: ,
          email: String | nil,
          service: String | nil,
          agency_code: Integer | nil,
          products:
        )
          begin
            return false unless coati_enabled?(store)

            raise 'No hay cuit para generar el envio' unless valid_cuit?(cuit)
            if !products.is_a?(Array) || products.size == 0
              raise 'No hay productos configurados para generar el envio'
            end
            raise 'No hay email para generar el envio' if email.nil?
            raise 'No hay service para generar el envio' if service.nil?
            if service == 'PICKUP' && agency_code.nil?
              raise 'Para servicios pickup debe informarse agencyCode'
            end

            body = {
              'carrier': carrier,
              'merchantCuit': cuit,
              'external_id': external_id.to_s,
              'agency_code': agency_code || nil,
              'service': service,
              'packages': products.map(&:to_sym),
              'origin': {
                'location': origin_address[:state],
                'zipCode': origin_address[:zip],
                'street': origin_address[:address],
                'number': origin_address[:street_number],
                'city': origin_address[:city],
                'province': origin_address[:state],
                'cellphone': origin_address[:telephone],
                'sender': 'Macropremia', # Ver q se agrega aca
                'documentType': origin_address[:doc_type],
                'document': origin_address[:doc_number],
                'floor': origin_address[:floor],
                'apartment': origin_address[:dpto]
              },
              'destination': {
                'name': destination_address[:first_name],
                'lastName': destination_address[:last_name],
                'location': destination_address[:city],
                'zipCode': destination_address[:zip],
                'street': destination_address[:address],
                'number': destination_address[:street_number],
                'city': destination_address[:state],
                'province': destination_address[:state],
                'cellphone': destination_address[:telephone],
                'email': email,
                'documentType': destination_address[:doc_type],
                'document': destination_address[:doc_number],
                'floor': destination_address[:floor],
                'apartment': destination_address[:dpto]
              }
            }.to_json

            response = request(:post, 'auth/orders', body)
            unless response.nil?
              result = JSON.parse(response.body)

              if response.code.to_i == 201
                return DeliveryResponse.new(result)
              elsif response.code.to_i == 409 # Si ya existe un pedido para este external_id trato de recuperar el tracking
                return get_shipping_info_by_external_id(external_id.to_s)
              elsif result.key?('error')
                raise "#{result['error']} - #{result['extraInfo']}"
              else
                raise "Unknown error with http code #{response.code}"
              end
            end
          rescue StandardError => e
            Rails.logger.info("Coati Error: #{e.message} para externalId: #{external_id}")
          end
          false
        end

        def get_shipping_info_by_external_id(external_id)
          begin
            return false unless coati_enabled?(store)

            response = request(:get, "auth/orders/external/#{external_id}")
            unless response.nil?
              result = JSON.parse(response.body)
              # Los datos necesario en el response son: trackind_id y _id
              # El primero mapea con tracking_number en el label
              # El segundo se guarda como coati_id dentro de gateway_data (se usa para gestionar devoluciones)
              if response.code.to_i == 200
                return DeliveryResponse.new(result)
              elsif result.key?('error')
                raise result['error']
              else
                raise "Unknown error with http code #{response.code}"
              end
            end
          rescue StandardError => e
            Rails.logger.info("Coati Error: #{e.message} para externalId: #{external_id}")
          end
          false
        end

        def self.create_return_shipping(
          store: store,
          tracking_number: String,
          reason: String | nil,
          products:
        )
          begin
            return false unless coati_enabled?(store)

            if tracking_number.nil?
              raise 'No hay tracking_number configurado para generar la devolución'
            end
            if !products.is_a?(Array) || products.size == 0
              raise 'No hay productos configurados para generar el envio'
            end

            body = {
              'packages': products.map(&:to_sym),
              'reason': reason
            }.to_json

            response = request(:post, "auth/orders/return/#{tracking_number}", body)

            unless response.nil?
              result = JSON.parse(response.body)
              if response.code.to_i == 201
                return DeliveryResponse.new(result)
              elsif response.code.to_i == 409 # Si ya existe un pedido para este external_id trato de recuperar el tracking
                return get_shipping_info_by_external_id(external_id.to_s)
              elsif result.key?('error')
                raise result['error']
              else
                raise "Unknown error with http code #{response.code}"
              end
            end
          rescue StandardError => e
            Rails.logger.info("Coati Error: #{e.message}")
          end
          false
        end

        def self.cancel_shipping(store: store, tracking_number:, reason: nil)
          return false unless coati_enabled?(store)
          return false if tracking_number.nil?

          body = { reason: reason }.to_json

          response = request(:patch, "auth/orders/cancel/#{tracking_number}", body)

          unless response.nil?
            result = JSON.parse(response.body) rescue {} # Responden con un string en algunos casos

            if response.code.to_i == 200
              return response.body # string
            elsif result.key?('error')
              raise result['error']
            else
              raise "Unknown error with http code #{response.code} - #{response.body}"
            end
          end
        rescue => e
          Rails.logger.error("Coati Error: #{e.class} - #{e.message}")
          false
        end

        private

        def self.request(method, endpoint, body = nil, headers = {})
          raise ArgumentError, "Unsupported method: #{method}" unless HTTP_METHODS.key?(method)

          url = URI("#{COATI_URL}/#{endpoint}")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          # TODO
          #http.verify_mode = OpenSSL::SSL::VERIFY_PEER # NO usar VERIFY_NONE en producción
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request_class = HTTP_METHODS[method]
          request = request_class.new(url)

          request.body = body if HTTP_METHODS_WITH_BODY.include?(method) && body

          request['apikey'] = COATI_API_KEY
          request['content-type'] = 'application/json'
          begin
            http.request(request)
          rescue StandardError => e
            Rails.logger.error("[CoatiClient] #{e.class}: #{e.message}")
            nil
          end
        end

        def self.valid_cuit?(cuit)
          !(cuit.nil? || cuit.empty?)
        end

        def self.coati_enabled?(store)
          store.coati_enabled
        end
      end
    end
  end
end
