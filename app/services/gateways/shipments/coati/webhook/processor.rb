module Gateways
  module Shipments
    module Coati
      module Webhook
        class Processor
          class ProcessingError < StandardError; end

          Result = Struct.new(:body, :status)

          def initialize(payload:, external_id:)
            @status = status_mapper(payload[:status])
            @external_id = external_id
          end

          def process!
            validate_status!

            suborder = find_suborder_by_external_id
            raise ProcessingError, 'Suborder not found' unless suborder

            shipment = suborder.shipment
            raise ProcessingError, 'Shipment not found' unless shipment

            validate_shipment_status!(shipment)

            # Si envían 'delivered' y es un 'refund' entonces es una devolución cancelada
            # Como es una cancelación asignamos el estado 'cancelled' al status
            @status = 'cancelled' if @status == 'delivered' && shipment.shipment_kind == 'refund'

            skip_post_actions = @status == 'cancelled'

            ::Mkp::StatusChange::EntityStatusManage.status_change(shipment,
                                                                  @status,
                                                                  skip_post_actions: skip_post_actions)

            Result.new('Shipment updated', :ok)
          rescue ProcessingError => e
            Result.new(e.message, :not_acceptable)
          end

          private

          def validate_status!
            valid_statuses = %w[not_delivered in_process delivered cancelled returned]

            return if valid_statuses.include?(@status)

            raise ProcessingError, "Status must be one of: #{valid_statuses.join(', ')}"
          end

          def validate_shipment_status!(shipment)
            if shipment.delivery? && !%w[delivered cancelled not_delivered].include?(@status)
              raise(
                ProcessingError,
                'Status could be delivered, cancelled or not_delivered for type delivery'
              )
            end

            return unless shipment.refund? && !%w[returned cancelled delivered].include?(@status)

            raise ProcessingError, 'Status could be cancelled or returned for type refund'
          end

          def find_suborder_by_external_id
            Mkp::Suborder.find_by(id: @external_id)
          end

          def status_mapper(coati_status)
            case coati_status
            when 'canceled', 'nullified'
              'cancelled'
            when 'returned to sender'
              'not_delivered'
            else
              coati_status
            end
          end
        end
      end
    end
  end
end
