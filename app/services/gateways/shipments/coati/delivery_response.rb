module Gateways
  module Shipments
    module Coati
      class DeliveryResponse
        attr_reader :tracking_number, :coati_id, :status

        def initialize(coati_response)
          raise 'La respuesta de Coati no es un hash' unless coati_response.is_a?(Hash)

          @tracking_number = parse_tracking_number(coati_response)
          @coati_id = parse_coati_id(coati_response)
          @status = parse_status(coati_response)
        end

        private

        def parse_tracking_number(coati_response)
          return coati_response['trackingNumber'] if coati_response.key?('trackingNumber')
          return coati_response['tracking_id'] if coati_response.key?('tracking_id')

          nil
        end

        def parse_coati_id(coati_response)
          # El id de coati se encuentra siempre dentro de un package
          if coati_response.key?('orderInfo') && coati_response['orderInfo'].is_a?(Hash)
            packages = coati_response.dig('orderInfo', 'packages')
          elsif coati_response.key?('packages')
            packages = coati_response['packages']
          else
            Rails.logger.info('Coati: No se puede obtener packages')
            return nil
          end

          return packages.last['_id'] if packages.is_a?(Array) && packages.last.key?('_id')

          nil
        end

        def parse_status(coati_response)
          return coati_response['status'] if coati_response.key?('status')

          nil
        end
      end
    end
  end
end
