module Gateways
  module Shipments
    module Coati
      class CoatiProduct
        attr_accessor :name, :sku, :price, :quantity, :image, :packages

        def initialize(name: nil,sku: nil, price: 0, quantity: 0, image: nil)
          @name = name
          @sku = sku
          @price = price
          @quantity = quantity
          @image = image
          @packages = []
        end

        def to_sym
          {
            name: @name,
            sku: @sku,
            price: @price.to_f,
            quantity: @quantity.to_i,
            image: @image,
            sub_packages: @packages.map(&:to_sym)
          }
        end
      end
    end
  end
end
