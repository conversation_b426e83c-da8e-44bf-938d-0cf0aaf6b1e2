module Gateways
  module Shipments
    module <PERSON><PERSON>
      class Package
        attr_reader :width, :height, :depth, :weight

        UNIT_KILOGRAMS = 'kilograms'.freeze
        UNIT_POUNDS = 'pounds'.freeze
        UNIT_INCHES = 'inches'.freeze
        UNIT_CENTIMETERS = 'centimeters'.freeze
        UNIT_MILLIMETERS = 'millimeters'.freeze

        %w[width height depth].each do |dimension|
          define_method(dimension.to_sym) do |value, unit|
            raise "CoatiPackage Error: invalid #{dimension} unit" unless valid_length_unit?(unit)

            instance_variable_set("@#{dimension}", conversion(value, unit, UNIT_CENTIMETERS).to_f)
          end
        end

        ['weight'].each do |mass|
          define_method(mass.to_sym) do |value, unit|
            raise "CoatiPackage Error: invalid #{mass} unit" unless valid_mass_unit?(unit)

            instance_variable_set("@#{mass}", conversion(value, unit, UNIT_KILOGRAMS).to_f)
          end
        end

        def initialize
          @width  = nil
          @height = nil
          @depth  = nil
          @weight = nil
        end

        def to_hash
          {
            'width' => @width,
            'height' => @height,
            'depth' => @depth,
            'weight' => @weight
          }
        end

        def to_sym
          {
            width: @width,
            height: @height,
            depth: @depth,
            weight: @weight
          }
        end

        private

        def valid_length_unit?(unit)
          return true if [UNIT_INCHES, UNIT_MILLIMETERS, UNIT_CENTIMETERS].include?(unit)

          false
        end

        def valid_mass_unit?(unit)
          return true if [UNIT_POUNDS, UNIT_KILOGRAMS].include?(unit)

          false
        end

        def conversion(value, from_unit, to_unit)
          raise "CoatiPackage Error: cannot convert nil value from #{from_unit} to #{to_unit}" if value.nil?

          if to_unit == UNIT_CENTIMETERS && valid_length_unit?(from_unit)
            case from_unit
            when UNIT_MILLIMETERS
              value / 10
            when UNIT_INCHES
              value * 2.54
            else
              value
            end
          elsif to_unit == UNIT_KILOGRAMS && valid_mass_unit?(from_unit)
            return value * 0.453592 if from_unit == UNIT_POUNDS

            value
          else
            raise "CoatiPackage Error: cannot convert value from #{from_unit} to #{to_unit}"
          end
        end
      end
    end
  end
end
