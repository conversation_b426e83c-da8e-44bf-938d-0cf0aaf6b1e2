module Gateways
  module Shipments
    module Coati
      class CancelShipmentService
        attr_accessor :valid, :response

        def initialize(label:)
          @label = label
          @shipment = label.shipment
          @valid = false
        end

        def perform(reason: nil)
          return false if tracking_number.blank?

          @response = Gateways::Shipments::Coati::Manager.cancel_shipping(
            store: @label.shipment.suborder.store,
            tracking_number: tracking_number,
            reason: reason || 'Cancelación desde sistema'
          )

          if @response.is_a?(FalseClass)
            Rails.logger.info("[coati:cancel] Falló cancelación para shipment ##{@shipment.id}")
            return false
          end

          Rails.logger.info("[coati:cancel] Envío #{@shipment.id} cancelado exitosamente")
          Mkp::StatusChange::EntityStatusManage.status_change(@shipment, 'cancelled')
          @valid = true
        end

        private

        def tracking_number
          @label.tracking_number
        end
      end
    end
  end
end
