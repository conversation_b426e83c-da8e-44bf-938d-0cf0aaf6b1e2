module Gateways
  module Shipments
    class Fulfiller
      def self.process_all_unfulfilled
        new.process_all
      end

      def process_all
        Rails.logger.info("--- #{shipments_scope.count} to fulfil")

        shipments_scope.find_in_batches(batch_size: 500) do |batch|
          batch.each do |shipment|
            next unless claim_shipment(shipment)

            process(shipment)
          end
        end
      end

      private

      def process(shipment)
        begin
          Rails.logger.info("Procesando los envios de las ordenes #{shipment.suborders.map(&:id).join(',')} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')}")
          Rails.logger.info("[fulfil_pending_shipments] Suborder Id: #{shipment.suborder_id} Proveedor logistico #{shipment.gateway_name} del seller #{shipment.suborder.shop.title}")

          case shipment.gateway_name
          when 'Krabpack'
            process_krabpack(shipment)
          when 'Coati'
            process_coati(shipment)
          else
            raise "Unknown gateway: #{shipment.gateway_name}"
          end

          shipment.update!(processing_started_at: nil, processing_error: nil)
        rescue StandardError => e
          if shipment.gateway_name == 'Coati'
            Rails.logger.info("Coati Error en envío #{shipment.id}: #{e.message}")
          end
          Rails.logger.error("[fulfil_pending_shipments] Error procesando envío #{shipment.id}: #{e.class} - #{e.message}")
          shipment.update!(processing_error: e.message.truncate(500))
        end
      end

      def shipments_scope
        store_shop_ids = Mkp::Store.find(41).shop_ids
        cutoff_date = 60.days.ago
        processing_expired_at = 60.minutes.ago

        Mkp::Shipment.joins(items: { product: :shop })
                     .includes(items: [{ suborder: :shop }, { product: :shop }])
                     .where(gateway: %w[Krabpack Coati], status: 'unfulfilled')
                     .where(mkp_shops: { id: store_shop_ids })
                     .where('mkp_shipments.created_at >= ?', cutoff_date)
                     .where('processing_started_at IS NULL OR processing_started_at < ?', processing_expired_at)
                     .distinct
      end

      def process_krabpack(shipment)
        service = Gateways::Shipments::Krabpack::CreateShipmentService.new(shipment: shipment)

        case shipment.shipment_kind
        when 'refund', 'exchange_refund'
          service.refund("#{(Time.zone.now + 1.day).strftime('%d/%m/%Y %H:%m:%S')},#{(Time.zone.now + 2.days).strftime('%d/%m/%Y %H:%m:%S')}", shipment.shipment_kind)
        when 'exchange_change'
          service.exchange
        else
          service.perform
        end

        errors = ''

        if !service.valid
          errors += "Error al procesando los envios de las ordenes #{shipment.suborders.map(&:id).join(',')} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')}"
          errors += '<br>'
          errors += service.error || ' ocurrio un error al generar el envio.'
          errors += '<br>'
        else
          Rails.logger.info("Los envios de las ordenes #{shipment.suborders.map(&:id).join(',')} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')} se procesaron correctamente")
        end

        IntegrationMailer.fulfil_pending_shipments(errors.html_safe).deliver
      end

      def process_coati(shipment)
        coati_products = shipment.items.map do |item|
          coati_product = Gateways::Shipments::Coati::CoatiProduct.new(
            sku: item.variant.gp_sku || nil,
            name: item.variant.title || nil,
            image: item.variant.product.pictures.first&.url(:t) || nil,
            price: item.variant.product.price || 0,
            quantity: item.quantity || 0
          )
          coati_product.packages = item.variant.product.packages.map do |package|
            p = Gateways::Shipments::Coati::Package.new
            p.width(package[:width], package[:length_unit])
            p.height(package[:height], package[:length_unit])
            p.depth(package[:length], package[:length_unit])
            p.weight(package[:weight], package[:mass_unit])
            p
          end
          coati_product
        end

        coati_response = if %w[refund exchange_refund].include?(shipment.shipment_kind)
                           Gateways::Shipments::Coati::Manager.create_return_shipping(
                             store: shipment.suborder.store,
                             tracking_number: shipment.gateway_data[:previous_tracking_number],
                             products: coati_products,
                             reason: ''
                           )
                         else
                           Gateways::Shipments::Coati::Manager.create_shipping(
                             store: shipment.suborder.store,
                             carrier: shipment.gateway_data[:carrier],
                             cuit: shipment.related_shops.first&.cuit,
                             external_id: shipment.gateway_object_id,
                             products: coati_products,
                             origin_address: shipment.origin_address,
                             destination_address: shipment.destination_address,
                             email: shipment.order.customer.email,
                             service: shipment.gateway_data[:service_level_real],
                             agency_code: shipment.gateway_data[:pickup_code]
                           )
                         end

        errors = ''

        # Si la comunicacion con Coati fue exitosa recibo el trackingNumber, sino devuelve false
        if coati_response.is_a?(FalseClass)
          Rails.logger.info("[fulfil_pending_shipments] El envio de la suborden #{shipment.suborder_id} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')} fue erroneo")
          errors += "Error al procesar el envio de la suborden #{shipment.suborder_id} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')} por Coati"
          errors += '<br>'
        else
          Rails.logger.info("[fulfil_pending_shipments] Tracking number #{coati_response.tracking_number} para el shipping #{shipment.id}")
          # Creo el label con el tracking number del shipping
          Mkp::ShipmentLabel.create(
            shipment_id: shipment.id,
            tracking_number: coati_response.tracking_number,
            courier: shipment.gateway_data[:carrier],
            price: shipment.charged_amount,
            estimated_delivery_date: Time.zone.now,
            gateway: 'Coati',
            gateway_object_id: shipment.gateway_object_id,
            gateway_data: { coati_id: coati_response.coati_id }
          )
          # Cambio el estado del envio a in_process
          Mkp::StatusChange::EntityStatusManage.status_change(shipment, 'in_process')
          Rails.logger.info("[fulfil_pending_shipments] El envio de la suborden #{shipment.suborder_id} del proveedor #{shipment.related_shops.map(&:name).join(' y del proveedor')} se proceso correctamente")
        end

        IntegrationMailer.fulfil_pending_shipments(errors.html_safe).deliver
      end

      def claim_shipment(shipment)
        rows = Mkp::Shipment.where(
          id: shipment.id
        ).where(
          "processing_started_at IS NULL OR processing_started_at < ?", Time.zone.now - 60.minutes
        ).update_all(processing_started_at: Time.zone.now)

        rows == 1
      end
    end
  end
end
