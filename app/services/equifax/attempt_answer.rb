module Equifax
  class AttemptAnswer < ApplicationService
    attr_reader :doc_number, :gender, :purchase_id, :current_store, :cart, :real_remote_ip, :quiz, :questions, :response, :equifax_token_api_endpoint, :equifax_client_api_endpoint

    include Api::AngularApp::V1::Concerns::RenaperVerifiable

    def initialize(args = {})
      @doc_number = args[:doc_number]
      @gender = args[:gender]
      @purchase_id = args[:purchase_id]
      @current_store = args[:current_store]
      @quiz = args[:quiz]
      @questions = @quiz.questions
      @cart = args[:cart]
      @real_remote_ip = args[:real_remote_ip]
      @equifax_token_api_endpoint = Rails.env.production? ? EQUIFAX_TOKEN_API_ENDPOINT_PRD : EQUIFAX_TOKEN_API_ENDPOINT_UAT
      @equifax_client_api_endpoint = Rails.env.production? ? EQUIFAX_CLIENT_API_ENDPOINT_PRD : EQUIFAX_CLIENT_API_ENDPOINT_UAT
      @response = @questions.dup

      equifax_log.info("equifax_token_api_endpoint: #{@equifax_token_api_endpoint} equifax_client_api_endpoint: #{@equifax_client_api_endpoint} quiz: #{quiz}")
    end

    def call
      begin
        token = nil
        status = :ok

        # Check blocking status using new service
        blocking_result = IdentityValidation::AttemptBlockingService.call(
          doc_number: doc_number,
          store_id: current_store.id
        )

        if blocking_result.blocked?
          cart.destroy if cart.present? # Al 12do intento se le debe vaciar el cart (removerlo)
          return OpenStruct.new(
            success: false,
            message: blocking_result.message,
            status: :unauthorized
          )
        end

        response_answers = send_response_equifax(quiz)
        equifax_log.info("Conexión con Equifax: #{response_answers}")

        if is_correct?(response_answers)
          response.each { |question| question["success"] = true }
          attempted_answer.each(&:destroy)
          pending_answer = EquifaxResponse.create(
            store_id: current_store.id,
            purchase_id: purchase_id,
            question_type: "MultipleOptions",
            doc_number: doc_number,
            gender: gender_customer_to_equifax(gender),
            answered: true,
            question: quiz.questionnaire_id
          )
          pending_answer.correct!
          token = pending_answer.token
        else
          status = 422
          response.each { |question| question["success"] = false }
          AnswerAttempt::Equifax.create({answer: questions.map { |q| q['response'] }, doc_number: doc_number, gender: gender_customer_to_equifax(gender), store_id: current_store.id, correct: false, ip: real_remote_ip, question_id: nil, question: quiz.questionnaire_id, question_type: "MultipleOptions"})
        end
        OpenStruct.new(type: :equifax, questions: response, token: token, status: status)
      rescue => e
        equifax_log.info("error: #{e}")
        OpenStruct.new(success: false, message: e.message, status: :internal_server_error)
      end
    end

    private

    def equifax_log
      equifax_log ||= Logger.new("#{Rails.root}/log/equifax.log")
    end

    def is_correct?(response_answers)
      response_answers['payload']['transactionStateCode'].to_i == 1
    end

    def send_response_equifax(quiz)
      equifax_log.info("Antes de enviar auth a equifax, quiz: #{quiz}")

      access_token = AvenidaWrappers::Equifax::Auth.new(
        username: current_store.equifax_id_validator_configuration.s2s_user,
        password: current_store.equifax_id_validator_configuration.s2s_password,
        equifax_token_api_endpoint: equifax_token_api_endpoint
      ).auth["access_token"]

      equifax_log.info("access_token: #{access_token}")

      payload = prepare_payload_answers(quiz)

      equifax_log.info("antes de enviar request a equifax")

      answers = AvenidaWrappers::Equifax::Client.new(
        auth_token: access_token,
        equifax_client_api_endpoint: equifax_client_api_endpoint
        ).answers(quiz.questionnaire_id, quiz.transaction_id, payload)

        equifax_log.info("despues de enviar request a equifax, answers: #{answers['payload']['transactionStateDescription']}")

      return answers
    end

    def prepare_payload_answers(quiz)
      {
        "idQuestionnaireGenerated": quiz.questionnaire_id,
        "idTransaction": quiz.transaction_id,
        "questionnaireResponse": quiz.questions.map do |response|
                                   {
                                     "idOption": response['response'],
                                     "idQuestion": response['id']
                                   }
                                  end
        }
    end

    def gender_customer_to_equifax(gender)
      case gender
        when 0, 'male'
          'M'
        when 1, 'female'
          'F'
        when 2, 'non_binary'
          'X'
      end
    end
  end
end
